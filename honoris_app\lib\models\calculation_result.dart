import 'package:json_annotation/json_annotation.dart';
import 'tier.dart';

part 'calculation_result.g.dart';

enum TierStatus { profit, loss, notCrafted }

@JsonSerializable()
class TierResult {
  final int quantity;
  final int requiredRawHides;
  final int requiredLeather;
  final double costPerUnit;
  final double rentalCostPerUnit;
  final double sellingPrice;
  final double netSellingPrice;
  final double buyingPricePrevTierMaterial;
  final double profitLossAmount;
  final double profitLossPercentage;
  final TierStatus status;

  const TierResult({
    required this.quantity,
    required this.requiredRawHides,
    required this.requiredLeather,
    required this.costPerUnit,
    required this.rentalCostPerUnit,
    required this.sellingPrice,
    required this.netSellingPrice,
    required this.buyingPricePrevTierMaterial,
    required this.profitLossAmount,
    required this.profitLossPercentage,
    required this.status,
  });

  factory TierResult.fromJson(Map<String, dynamic> json) =>
      _$TierResultFromJson(json);

  Map<String, dynamic> toJson() => _$TierResultToJson(this);
}

@JsonSerializable()
class CalculationSummary {
  final double totalRawHideCost;
  final double totalAcquiredLeatherCost;
  final double totalMaterialInvestment;
  final double totalRentalCost;
  final double totalRevenue;
  final double netProfitLoss;
  final double netProfitLossPercentage;

  const CalculationSummary({
    required this.totalRawHideCost,
    required this.totalAcquiredLeatherCost,
    required this.totalMaterialInvestment,
    required this.totalRentalCost,
    required this.totalRevenue,
    required this.netProfitLoss,
    required this.netProfitLossPercentage,
  });

  factory CalculationSummary.fromJson(Map<String, dynamic> json) =>
      _$CalculationSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$CalculationSummaryToJson(this);
}

@JsonSerializable()
class CalculationResult {
  final CalculationSummary summary;
  final Map<Tier, TierResult> tierResults;

  const CalculationResult({
    required this.summary,
    required this.tierResults,
  });

  factory CalculationResult.fromJson(Map<String, dynamic> json) =>
      _$CalculationResultFromJson(json);

  Map<String, dynamic> toJson() => _$CalculationResultToJson(this);
}
