import 'package:json_annotation/json_annotation.dart';
import 'ingredient.dart';

part 'crafting_product.g.dart';

@JsonSerializable()
class CraftingProduct {
  final String id;
  final String name;
  final int objectPower; // Poder del objeto para calcular costo de tienda
  final int craftedQuantity; // Cantidad que sale por lote
  final List<Ingredient> ingredients; // Ingredientes necesarios
  final double focusPerBatch; // Foco adicional por lote (opcional)
  final bool isRawMaterial; // Si puede ser usado como ingrediente de otros productos
  final DateTime createdAt;
  final DateTime updatedAt;

  const CraftingProduct({
    required this.id,
    required this.name,
    required this.objectPower,
    required this.craftedQuantity,
    required this.ingredients,
    required this.focusPerBatch,
    required this.isRawMaterial,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CraftingProduct.fromJson(Map<String, dynamic> json) =>
      _$CraftingProductFromJson(json);

  Map<String, dynamic> toJson() => _$CraftingProductToJson(this);

  CraftingProduct copyWith({
    String? id,
    String? name,
    int? objectPower,
    int? craftedQuantity,
    List<Ingredient>? ingredients,
    double? focusPerBatch,
    bool? isRawMaterial,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CraftingProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      objectPower: objectPower ?? this.objectPower,
      craftedQuantity: craftedQuantity ?? this.craftedQuantity,
      ingredients: ingredients ?? this.ingredients,
      focusPerBatch: focusPerBatch ?? this.focusPerBatch,
      isRawMaterial: isRawMaterial ?? this.isRawMaterial,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Costo total de ingredientes por lote
  double get totalIngredientCost {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.totalCost);
  }

  /// Foco total por lote (ingredientes + foco adicional)
  double get totalFocusPerBatch {
    final ingredientsFocus = ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.totalFocus);
    return ingredientsFocus + focusPerBatch;
  }

  /// Costo por unidad individual
  double get costPerUnit {
    return totalIngredientCost / craftedQuantity;
  }

  /// Foco por unidad individual
  double get focusPerUnit {
    return totalFocusPerBatch / craftedQuantity;
  }

  /// Verifica si se pueden fabricar los lotes especificados con el inventario disponible
  bool canCraft(int batches, Map<String, double> availableQuantities) {
    for (final ingredient in ingredients) {
      final available = availableQuantities[ingredient.itemId] ?? 0;
      final needed = ingredient.quantity * batches;
      if (available < needed) {
        return false;
      }
    }
    return true;
  }

  /// Calcula los ingredientes necesarios para un número específico de lotes
  Map<String, double> getRequiredIngredients(int batches) {
    final required = <String, double>{};
    for (final ingredient in ingredients) {
      required[ingredient.itemId] = ingredient.quantity * batches;
    }
    return required;
  }
}
