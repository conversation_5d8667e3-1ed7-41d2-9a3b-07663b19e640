import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/calculation_provider.dart';
import '../models/calculation_result.dart';
import '../models/tier.dart';

class ResultsStep extends StatelessWidget {
  const ResultsStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CalculationProvider>(
      builder: (context, provider, child) {
        if (provider.result == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.calculate,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'Presiona "Calcular" para ver los resultados',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        final result = provider.result!;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Paso 3: Resultados',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Aquí están los resultados de tu cálculo de refinado.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 24),

              // Resumen General
              _buildSummaryCard(context, result.summary),
              const SizedBox(height: 16),

              // Resultados por Tier
              Text(
                'Resultados por Tier',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              ...result.tierResults.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: _buildTierResultCard(context, entry.key, entry.value),
                );
              }).toList(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCard(BuildContext context, CalculationSummary summary) {
    final isProfit = summary.netProfitLoss >= 0;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Resumen General',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildSummaryRow(
              context,
              'Costo Total en Materias Primas Adquiridas:',
              _formatNumber(summary.totalRawHideCost),
            ),
            
            if (summary.totalAcquiredLeatherCost > 0)
              _buildSummaryRow(
                context,
                'Costo Total en Refinado Adquirido:',
                _formatNumber(summary.totalAcquiredLeatherCost),
              ),
            
            _buildSummaryRow(
              context,
              'Inversión Total en Materiales Adquiridos:',
              _formatNumber(summary.totalMaterialInvestment),
            ),
            
            _buildSummaryRow(
              context,
              'Costo Total de Alquiler:',
              _formatNumber(summary.totalRentalCost),
            ),
            
            _buildSummaryRow(
              context,
              'Ingresos Totales (Tier Límite):',
              _formatNumber(summary.totalRevenue),
            ),
            
            const Divider(),
            
            _buildSummaryRow(
              context,
              'Ganancia/Pérdida Neta:',
              _formatNumber(summary.netProfitLoss),
              color: isProfit ? Colors.green : Colors.red,
              isBold: true,
            ),
            
            _buildSummaryRow(
              context,
              'Ganancia/Pérdida Neta (%):',
              '${_formatNumber(summary.netProfitLossPercentage)}%',
              color: isProfit ? Colors.green : Colors.red,
              isBold: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    Color? color,
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTierResultCard(BuildContext context, Tier tier, TierResult result) {
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    switch (result.status) {
      case TierStatus.profit:
        statusColor = Colors.green;
        statusText = '¡Sí vender!';
        statusIcon = Icons.trending_up;
        break;
      case TierStatus.loss:
        statusColor = Colors.red;
        statusText = '¡No vender!';
        statusIcon = Icons.trending_down;
        break;
      case TierStatus.notCrafted:
        statusColor = Colors.grey;
        statusText = 'No Crafteado';
        statusIcon = Icons.block;
        break;
    }

    return Card(
      color: statusColor.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '${tier.displayName} Refinado',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildResultRow(context, 'Cantidad Producida:', result.quantity.toString()),
            _buildResultRow(context, 'Materias Primas en Bruto Requeridas:', result.requiredRawHides.toString()),
            
            if (result.requiredLeather > 0)
              _buildResultRow(context, 'Refinado Requerido (${tier.previous?.displayName ?? ''}):', result.requiredLeather.toString()),
            
            if (result.rentalCostPerUnit > 0)
              _buildResultRow(context, 'Costo por Unidad (Alquiler):', _formatNumber(result.rentalCostPerUnit)),
            
            _buildResultRow(context, 'Costo por Unidad (Calculado):', _formatNumber(result.costPerUnit)),
            _buildResultRow(context, 'Precio de Venta Neto:', _formatNumber(result.netSellingPrice)),
            
            if (result.buyingPricePrevTierMaterial > 0)
              _buildResultRow(context, 'Precio Compra Refinado Anterior:', _formatNumber(result.buyingPricePrevTierMaterial)),
            
            const Divider(),
            
            _buildResultRow(
              context,
              'Ganancia/Pérdida (Cantidad):',
              _formatNumber(result.profitLossAmount),
              color: result.profitLossAmount >= 0 ? Colors.green : Colors.red,
            ),
            
            _buildResultRow(
              context,
              'Ganancia/Pérdida (%):',
              '${_formatNumber(result.profitLossPercentage)}%',
              color: result.profitLossPercentage >= 0 ? Colors.green : Colors.red,
            ),
            
            if (result.status == TierStatus.profit)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Ganancia: ${_formatNumber(result.profitLossAmount)} (${_formatNumber(result.profitLossPercentage)}%)',
                  style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                ),
              ),
            
            if (result.status == TierStatus.loss)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Pérdida: ${_formatNumber(result.profitLossAmount.abs())} (${_formatNumber(result.profitLossPercentage.abs())}%)',
                  style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String label,
    String value, {
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: color != null ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  String _formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString();
    }
    return number.toStringAsFixed(2);
  }
}
