import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/inventory_item.dart';
import '../models/crafting_product.dart';
import '../models/harvest_data.dart';
import '../models/breeding_data.dart';

class InventoryService {
  static const String _inventoryKey = 'inventory_items';
  static const String _productsKey = 'crafting_products';

  /// Obtiene todos los items del inventario
  static Future<List<InventoryItem>> getInventoryItems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = prefs.getString(_inventoryKey);

      if (itemsJson == null) {
        return [];
      }

      final List<dynamic> itemsList = json.decode(itemsJson);
      return itemsList.map((json) => InventoryItem.fromJson(json)).toList();
    } catch (e) {
      print('Error loading inventory items: $e');
      return [];
    }
  }

  /// Obtiene todos los productos de fabricación
  static Future<List<CraftingProduct>> getCraftingProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = prefs.getString(_productsKey);

      if (productsJson == null) {
        return [];
      }

      final List<dynamic> productsList = json.decode(productsJson);
      return productsList
          .map((json) => CraftingProduct.fromJson(json))
          .toList();
    } catch (e) {
      print('Error loading crafting products: $e');
      return [];
    }
  }

  /// Guarda un item en el inventario (combina si ya existe)
  static Future<bool> saveInventoryItem(InventoryItem item) async {
    try {
      final currentItems = await getInventoryItems();

      // Buscar si ya existe un item con el mismo nombre
      final existingIndex = currentItems.indexWhere(
        (existing) => existing.name == item.name,
      );

      if (existingIndex != -1) {
        // Combinar con el existente usando promedio ponderado
        currentItems[existingIndex] = currentItems[existingIndex].combineWith(
          item,
        );
      } else {
        // Agregar como nuevo item
        currentItems.add(item);
      }

      return await _saveInventoryItems(currentItems);
    } catch (e) {
      print('Error saving inventory item: $e');
      return false;
    }
  }

  /// Guarda un producto de fabricación
  static Future<bool> saveCraftingProduct(CraftingProduct product) async {
    try {
      final currentProducts = await getCraftingProducts();

      // Buscar si ya existe un producto con el mismo ID
      final existingIndex = currentProducts.indexWhere(
        (existing) => existing.id == product.id,
      );

      if (existingIndex != -1) {
        // Reemplazar el existente
        currentProducts[existingIndex] = product;
      } else {
        // Agregar como nuevo producto
        currentProducts.add(product);
      }

      return await _saveCraftingProducts(currentProducts);
    } catch (e) {
      print('Error saving crafting product: $e');
      return false;
    }
  }

  /// Crea un item de inventario desde datos de cosecha
  static InventoryItem createFromHarvest(HarvestData harvestData) {
    return InventoryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: harvestData.cropName,
      price: harvestData.pricePerUnit,
      quantity: harvestData.harvestReceived.toDouble(),
      focusUsed: harvestData.focusPerUnit,
      origin: ItemOrigin.harvest,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isRawMaterial: true,
    );
  }

  /// Crea un item de inventario desde datos de cría
  static InventoryItem createFromBreeding(BreedingData breedingData) {
    return InventoryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: breedingData.animalName,
      price: breedingData.pricePerUnit,
      quantity: breedingData.youngPurchased.toDouble(),
      focusUsed: breedingData.focusPerUnit,
      origin: ItemOrigin.breeding,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isRawMaterial: true,
    );
  }

  /// Crea un item de inventario desde compra directa
  static InventoryItem createFromDirectPurchase({
    required String name,
    required double price,
    required double quantity,
    required bool purchasedInMarket,
  }) {
    final effectivePrice = purchasedInMarket ? price * 1.025 : price;

    return InventoryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      price: effectivePrice,
      quantity: quantity,
      focusUsed: 0.0, // Compra directa no usa foco
      origin: ItemOrigin.directPurchase,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isRawMaterial: true,
    );
  }

  /// Crea un item de inventario desde fabricación
  static InventoryItem createFromCrafting({
    required CraftingProduct product,
    required int quantityProduced,
    required double costPerUnit,
    required double focusPerUnit,
  }) {
    return InventoryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: product.name,
      price: costPerUnit,
      quantity: quantityProduced.toDouble(),
      focusUsed: focusPerUnit,
      origin: ItemOrigin.crafted,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isRawMaterial: product.isRawMaterial,
    );
  }

  /// Elimina un item del inventario
  static Future<bool> deleteInventoryItem(String id) async {
    try {
      final currentItems = await getInventoryItems();
      currentItems.removeWhere((item) => item.id == id);
      return await _saveInventoryItems(currentItems);
    } catch (e) {
      print('Error deleting inventory item: $e');
      return false;
    }
  }

  /// Elimina un producto de fabricación
  static Future<bool> deleteCraftingProduct(String id) async {
    try {
      final currentProducts = await getCraftingProducts();
      currentProducts.removeWhere((product) => product.id == id);
      return await _saveCraftingProducts(currentProducts);
    } catch (e) {
      print('Error deleting crafting product: $e');
      return false;
    }
  }

  /// Reduce la cantidad de un item en el inventario
  static Future<bool> consumeInventoryItem(String id, double quantity) async {
    try {
      final currentItems = await getInventoryItems();
      final itemIndex = currentItems.indexWhere((item) => item.id == id);

      if (itemIndex == -1) {
        return false; // Item no encontrado
      }

      final item = currentItems[itemIndex];
      if (item.quantity < quantity) {
        return false; // No hay suficiente cantidad
      }

      currentItems[itemIndex] = item.reduceQuantity(quantity);
      return await _saveInventoryItems(currentItems);
    } catch (e) {
      print('Error consuming inventory item: $e');
      return false;
    }
  }

  /// Obtiene items que pueden ser usados como ingredientes (materias primas + productos marcados como tal)
  static Future<List<InventoryItem>> getAvailableIngredients() async {
    final items = await getInventoryItems();
    final products = await getCraftingProducts();

    final ingredients = <InventoryItem>[];

    // Agregar materias primas
    ingredients.addAll(items.where((item) => item.isRawMaterial));

    // Agregar productos que pueden ser ingredientes
    for (final product in products.where((p) => p.isRawMaterial)) {
      // Buscar si hay stock del producto en el inventario
      final productInInventory = items.firstWhere(
        (item) => item.name == product.name && !item.isRawMaterial,
        orElse: () => InventoryItem(
          id: product.id,
          name: product.name,
          price: product.costPerUnit,
          quantity: 0,
          focusUsed: product.focusPerUnit,
          origin: ItemOrigin.crafted,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          isRawMaterial: false,
        ),
      );

      ingredients.add(productInInventory);
    }

    return ingredients;
  }

  /// Guarda la lista completa de items del inventario
  static Future<bool> _saveInventoryItems(List<InventoryItem> items) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = json.encode(
        items.map((item) => item.toJson()).toList(),
      );
      return await prefs.setString(_inventoryKey, itemsJson);
    } catch (e) {
      print('Error saving inventory items: $e');
      return false;
    }
  }

  /// Guarda la lista completa de productos de fabricación
  static Future<bool> _saveCraftingProducts(
    List<CraftingProduct> products,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = json.encode(
        products.map((product) => product.toJson()).toList(),
      );
      return await prefs.setString(_productsKey, productsJson);
    } catch (e) {
      print('Error saving crafting products: $e');
      return false;
    }
  }

  /// Limpia todo el inventario
  static Future<bool> clearInventory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_inventoryKey);
      await prefs.remove(_productsKey);
      return true;
    } catch (e) {
      print('Error clearing inventory: $e');
      return false;
    }
  }
}
