// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ingredient.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Ingredient _$IngredientFromJson(Map<String, dynamic> json) => Ingredient(
  itemId: json['itemId'] as String,
  itemName: json['itemName'] as String,
  quantity: (json['quantity'] as num).toDouble(),
  pricePerUnit: (json['pricePerUnit'] as num).toDouble(),
  focusPerUnit: (json['focusPerUnit'] as num).toDouble(),
);

Map<String, dynamic> _$IngredientToJson(Ingredient instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'itemName': instance.itemName,
      'quantity': instance.quantity,
      'pricePerUnit': instance.pricePerUnit,
      'focusPerUnit': instance.focusPerUnit,
    };
