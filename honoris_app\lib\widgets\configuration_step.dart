import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/calculation_provider.dart';

class ConfigurationStep extends StatelessWidget {
  const ConfigurationStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CalculationProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Paso 1: Configuración',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Configura los parámetros básicos para el cálculo de refinado.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 24),

              // Costo de Alquiler
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Costo del Alquiler del Local (%)',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        initialValue: provider.config.rentalCost.toString(),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        decoration: const InputDecoration(
                          hintText: '0.0',
                          suffixText: '%',
                        ),
                        onChanged: (value) {
                          final doubleValue = double.tryParse(value) ?? 0.0;
                          provider.updateRentalCost(doubleValue);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Tasas de Mercado
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tasas de Mercado',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Tasa de Compra
                      Text(
                        'Tasa de Compra (%)',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<double>(
                        value: provider.config.purchasePercentage,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona la tasa de compra',
                        ),
                        items: const [
                          DropdownMenuItem(value: 0.0, child: Text('0% (Recolectado por ti)')),
                          DropdownMenuItem(value: 2.5, child: Text('2.5% (Comprado en mercado)')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            provider.updatePurchasePercentage(value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Tasa de Venta
                      Text(
                        'Tasa de Venta (%)',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<double>(
                        value: provider.config.salesPercentage,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona la tasa de venta',
                        ),
                        items: const [
                          DropdownMenuItem(value: 0.0, child: Text('0% (Sin tasa)')),
                          DropdownMenuItem(value: 4.0, child: Text('4% (Con premium)')),
                          DropdownMenuItem(value: 8.0, child: Text('8% (Sin premium)')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            provider.updateSalesPercentage(value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Impuestos
                      Text(
                        'Impuestos (%)',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<double>(
                        value: provider.config.taxPercentage,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona los impuestos',
                        ),
                        items: const [
                          DropdownMenuItem(value: 0.0, child: Text('0%')),
                          DropdownMenuItem(value: 2.5, child: Text('2.5%')),
                          DropdownMenuItem(value: 5.0, child: Text('5.0%')),
                          DropdownMenuItem(value: 7.5, child: Text('7.5%')),
                          DropdownMenuItem(value: 10.0, child: Text('10.0%')),
                          DropdownMenuItem(value: 12.5, child: Text('12.5%')),
                          DropdownMenuItem(value: 15.0, child: Text('15.0%')),
                          DropdownMenuItem(value: 17.5, child: Text('17.5%')),
                          DropdownMenuItem(value: 20.0, child: Text('20.0%')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            provider.updateTaxPercentage(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Retorno de Recursos
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Retorno de Recursos (%)',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Selecciona el porcentaje de retorno según tu ubicación y bonificaciones:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<double>(
                        value: provider.config.returnPercentage,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona el retorno',
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 36.7,
                            child: Text('36.7% (Ciudad sin foco, sin premium)'),
                          ),
                          DropdownMenuItem(
                            value: 43.5,
                            child: Text('43.5% (Con foco o premium)'),
                          ),
                          DropdownMenuItem(
                            value: 53.9,
                            child: Text('53.9% (Ciudad con bono + foco)'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            provider.updateReturnPercentage(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }
}
