// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'harvest_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HarvestData _$HarvestDataFromJson(Map<String, dynamic> json) => HarvestData(
  cropName: json['cropName'] as String,
  seedPrice: (json['seedPrice'] as num).toDouble(),
  purchasedInMarket: json['purchasedInMarket'] as bool,
  seedsPurchased: (json['seedsPurchased'] as num).toInt(),
  seedsReceived: (json['seedsReceived'] as num).toInt(),
  harvestReceived: (json['harvestReceived'] as num).toInt(),
  focusPerSeed: (json['focusPerSeed'] as num).toDouble(),
);

Map<String, dynamic> _$HarvestDataToJson(HarvestData instance) =>
    <String, dynamic>{
      'cropName': instance.cropName,
      'seedPrice': instance.seedPrice,
      'purchasedInMarket': instance.purchasedInMarket,
      'seedsPurchased': instance.seedsPurchased,
      'seedsReceived': instance.seedsReceived,
      'harvestReceived': instance.harvestReceived,
      'focusPerSeed': instance.focusPerSeed,
    };
