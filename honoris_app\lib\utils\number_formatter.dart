import 'package:intl/intl.dart';

class NumberFormatter {
  static final NumberFormat _currencyFormat = NumberFormat.currency(
    locale: 'es_ES',
    symbol: '\$',
    decimalDigits: 2,
  );

  static final NumberFormat _numberFormat = NumberFormat(
    '#,##0.##',
    'es_ES',
  );

  static final NumberFormat _integerFormat = NumberFormat(
    '#,##0',
    'es_ES',
  );

  /// Formatea un número como moneda: $123.456,52
  static String formatCurrency(double value) {
    return _currencyFormat.format(value);
  }

  /// Formatea un número decimal: 123.456,52
  static String formatNumber(double value) {
    return _numberFormat.format(value);
  }

  /// Formatea un número entero: 123.456
  static String formatInteger(int value) {
    return _integerFormat.format(value);
  }

  /// Formatea un porcentaje: 12,5%
  static String formatPercentage(double value) {
    return '${_numberFormat.format(value)}%';
  }

  /// Parsea un string con formato español a double
  static double parseNumber(String value) {
    try {
      // Remover símbolo de moneda si existe
      String cleanValue = value.replaceAll('\$', '').trim();
      
      // Reemplazar puntos por nada (separador de miles)
      // y comas por puntos (separador decimal)
      cleanValue = cleanValue.replaceAll('.', '').replaceAll(',', '.');
      
      return double.parse(cleanValue);
    } catch (e) {
      return 0.0;
    }
  }

  /// Parsea un string con formato español a int
  static int parseInteger(String value) {
    try {
      String cleanValue = value.replaceAll('.', '');
      return int.parse(cleanValue);
    } catch (e) {
      return 0;
    }
  }
}
