/// Servicio para calcular costos de alquiler basados en el valor del objeto
class RentalCostService {
  /// Calcula el costo de alquiler por lote basado en el valor del objeto
  /// 
  /// Fórmula:
  /// Alquiler = (PrecioDelAlquiler / 100) × (CantidadCrafteada × ValorDelObjeto) × 0.1125
  static double calculateRentalCost({
    required int objectValue, // antes objectPower
    required int batches,
    required int unitsPerBatch,
    double baseRentalRate = 500.0, // Tasa base de alquiler
  }) {
    // Fórmula actualizada según requerimiento
    final costPerBatch = (baseRentalRate / 100.0) * (unitsPerBatch * objectValue) * 0.1125;
    return costPerBatch * batches;
  }

  // Ya no se requiere el cálculo de multiplicador de poder

  /// Obtiene información detallada del costo de alquiler
  static Map<String, dynamic> getRentalCostBreakdown({
    required int objectValue,
    required int batches,
    required int unitsPerBatch,
    double baseRentalRate = 500.0,
  }) {
    final costPerBatch = (baseRentalRate / 100.0) * (unitsPerBatch * objectValue) * 0.1125;
    final totalCost = costPerBatch * batches;
    return {
      'valorDelObjeto': objectValue,
      'batches': batches,
      'unitsPerBatch': unitsPerBatch,
      'baseRentalRate': baseRentalRate,
      'costPerBatch': costPerBatch,
      'totalCost': totalCost,
    };
  }

  // Ya no se requiere el nombre del tier

  /// Calcula el costo de alquiler por unidad producida
  static double calculateRentalCostPerUnit({
    required int objectValue,
    required int batches,
    required int unitsPerBatch,
    double baseRentalRate = 500.0,
  }) {
    final totalRentalCost = calculateRentalCost(
      objectValue: objectValue,
      batches: batches,
      unitsPerBatch: unitsPerBatch,
      baseRentalRate: baseRentalRate,
    );
    final totalUnits = batches * unitsPerBatch;
    return totalUnits > 0 ? totalRentalCost / totalUnits : 0.0;
  }
}
