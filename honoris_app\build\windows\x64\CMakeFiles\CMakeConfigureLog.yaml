
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22621 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Versión de MSBuild 17.8.5+b5265ef37 para .NET Framework
      Compilación iniciada a las 6/7/2025 3:01:21 p. m..
      
      Proyecto "E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\3.27.2-msvc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
        Creando directorio "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Se creará "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\3.27.2-msvc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminará el archivo "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilación del proyecto terminada "E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\3.27.2-msvc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinos predeterminados).
      
      Compilación correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:03.08
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/3.27.2-msvc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-hk6pz5"
      binary: "E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-hk6pz5"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-hk6pz5'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f7aac.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.8.5+b5265ef37 para .NET Framework
        Compilación iniciada a las 6/7/2025 3:01:26 p. m..
        
        Proyecto "E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-hk6pz5\\cmTC_f7aac.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_f7aac.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-hk6pz5\\Debug\\".
          Creando directorio "cmTC_f7aac.dir\\Debug\\cmTC_f7aac.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_f7aac.dir\\Debug\\cmTC_f7aac.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_f7aac.dir\\Debug\\cmTC_f7aac.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f7aac.dir\\Debug\\\\" /Fd"cmTC_f7aac.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.27\\Modules\\CMakeCXXCompilerABI.cpp"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.38.33135 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f7aac.dir\\Debug\\\\" /Fd"cmTC_f7aac.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.27\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-hk6pz5\\Debug\\cmTC_f7aac.exe" /INCREMENTAL /ILK:"cmTC_f7aac.dir\\Debug\\cmTC_f7aac.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-hk6pz5/Debug/cmTC_f7aac.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/herd/honoris_old/honoris_app/build/windows/x64/CMakeFiles/CMakeScratch/TryCompile-hk6pz5/Debug/cmTC_f7aac.lib" /MACHINE:X64  /machine:x64 cmTC_f7aac.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_f7aac.vcxproj -> E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-hk6pz5\\Debug\\cmTC_f7aac.exe
        FinalizeBuildStatus:
          Se eliminará el archivo "cmTC_f7aac.dir\\Debug\\cmTC_f7aac.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_f7aac.dir\\Debug\\cmTC_f7aac.tlog\\cmTC_f7aac.lastbuildstate".
        Compilación del proyecto terminada "E:\\herd\\honoris_old\\honoris_app\\build\\windows\\x64\\CMakeFiles\\CMakeScratch\\TryCompile-hk6pz5\\cmTC_f7aac.vcxproj" (destinos predeterminados).
        
        Compilación correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:01.31
        
      exitCode: 0
...
