import 'package:json_annotation/json_annotation.dart';

part 'breeding_data.g.dart';

@JsonSerializable()
class BreedingData {
  final String animalName;
  final double youngPrice;
  final bool purchasedInMarket; // true = mercado (2.5%), false = isla (0%)
  final int youngPurchased;
  final int youngReceived; // Crías que nacieron
  final double focusPerYoung; // Foco usado por cría (opcional)
  final double foodPerDay; // Cantidad de alimento por día (normalmente 9)
  final String foodItemId; // ID del alimento en el inventario
  final String foodItemName; // Nombre del alimento
  final double foodPrice; // Precio del alimento por unidad
  final double foodFocus; // Foco del alimento por unidad
  final int timeToGrow; // Tiempo en horas para que nazca el animal

  const BreedingData({
    required this.animalName,
    required this.youngPrice,
    required this.purchasedInMarket,
    required this.youngPurchased,
    required this.youngReceived,
    required this.focusPerYoung,
    required this.foodPerDay,
    required this.foodItemId,
    required this.foodItemName,
    required this.foodPrice,
    required this.foodFocus,
    required this.timeToGrow,
  });

  factory BreedingData.fromJson(Map<String, dynamic> json) =>
      _$BreedingDataFromJson(json);

  Map<String, dynamic> toJson() => _$BreedingDataToJson(this);

  /// Calcula el precio por unidad del animal adulto
  double get pricePerUnit {
    if (youngPurchased == 0) return 0;
    
    final effectiveYoungPrice = purchasedInMarket 
        ? youngPrice * 1.025 // Agregar 2.5% de tasa
        : youngPrice;
    
    // Costo de las crías perdidas
    final youngLost = youngPurchased - youngReceived;
    final youngCost = youngLost * effectiveYoungPrice;
    
    // Costo de alimentación
    final totalFoodNeeded = youngPurchased * foodPerDay * (timeToGrow / 24);
    final foodCost = totalFoodNeeded * foodPrice;
    
    final totalCost = youngCost + foodCost;
    return totalCost / youngPurchased;
  }

  /// Calcula el foco por unidad del animal adulto
  double get focusPerUnit {
    if (youngPurchased == 0) return 0;
    
    // Foco directo por cría
    final directFocus = youngPurchased * focusPerYoung;
    
    // Foco del alimento
    final totalFoodNeeded = youngPurchased * foodPerDay * (timeToGrow / 24);
    final foodFocusTotal = totalFoodNeeded * foodFocus;
    
    final totalFocus = directFocus + foodFocusTotal;
    return totalFocus / youngPurchased;
  }

  /// Valida que los datos sean correctos
  List<String> validate() {
    final errors = <String>[];
    
    if (animalName.trim().isEmpty) {
      errors.add('El nombre del animal es requerido');
    }
    
    if (youngPrice <= 0) {
      errors.add('El precio de la cría debe ser mayor a 0');
    }
    
    if (youngPurchased <= 0) {
      errors.add('Las crías compradas deben ser mayor a 0');
    }
    
    if (youngReceived < 0) {
      errors.add('Las crías recibidas no pueden ser negativas');
    }
    
    if (youngReceived > youngPurchased) {
      errors.add('Las crías recibidas no pueden ser mayor a las compradas');
    }
    
    if (focusPerYoung < 0) {
      errors.add('El foco por cría no puede ser negativo');
    }
    
    if (foodPerDay <= 0) {
      errors.add('La cantidad de alimento por día debe ser mayor a 0');
    }
    
    if (foodItemId.trim().isEmpty) {
      errors.add('Debe seleccionar un alimento del inventario');
    }
    
    if (foodPrice < 0) {
      errors.add('El precio del alimento no puede ser negativo');
    }
    
    if (foodFocus < 0) {
      errors.add('El foco del alimento no puede ser negativo');
    }
    
    if (timeToGrow <= 0) {
      errors.add('El tiempo de crecimiento debe ser mayor a 0');
    }
    
    return errors;
  }
}
