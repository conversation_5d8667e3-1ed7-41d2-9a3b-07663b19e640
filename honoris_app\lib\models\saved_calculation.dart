import 'package:json_annotation/json_annotation.dart';
import 'calculation_config.dart';

part 'saved_calculation.g.dart';

@JsonSerializable()
class SavedCalculation {
  final String id;
  final String name;
  final DateTime createdAt;
  final CalculationConfig config;

  const SavedCalculation({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.config,
  });

  factory SavedCalculation.fromJson(Map<String, dynamic> json) =>
      _$SavedCalculationFromJson(json);

  Map<String, dynamic> toJson() => _$SavedCalculationToJson(this);

  SavedCalculation copyWith({
    String? id,
    String? name,
    DateTime? createdAt,
    CalculationConfig? config,
  }) {
    return SavedCalculation(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      config: config ?? this.config,
    );
  }
}
