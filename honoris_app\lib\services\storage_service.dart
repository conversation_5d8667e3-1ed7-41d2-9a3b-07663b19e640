import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/saved_calculation.dart';

class StorageService {
  static const String _calculationsKey = 'saved_calculations';
  static const int _maxCalculations = 10;

  /// Obtiene todas las calculaciones guardadas
  static Future<List<SavedCalculation>> getSavedCalculations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final calculationsJson = prefs.getString(_calculationsKey);
      
      if (calculationsJson == null) {
        return [];
      }

      final List<dynamic> calculationsList = json.decode(calculationsJson);
      return calculationsList
          .map((json) => SavedCalculation.fromJson(json))
          .toList();
    } catch (e) {
      print('Error loading saved calculations: $e');
      return [];
    }
  }

  /// Guarda una nueva calculación
  static Future<bool> saveCalculation(SavedCalculation calculation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCalculations = await getSavedCalculations();

      // Agregar la nueva calculación al inicio de la lista
      currentCalculations.insert(0, calculation);

      // Limitar el número de calculaciones guardadas
      if (currentCalculations.length > _maxCalculations) {
        currentCalculations.removeRange(_maxCalculations, currentCalculations.length);
      }

      // Convertir a JSON y guardar
      final calculationsJson = json.encode(
        currentCalculations.map((calc) => calc.toJson()).toList(),
      );

      return await prefs.setString(_calculationsKey, calculationsJson);
    } catch (e) {
      print('Error saving calculation: $e');
      return false;
    }
  }

  /// Elimina una calculación por ID
  static Future<bool> deleteCalculation(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCalculations = await getSavedCalculations();

      // Filtrar la calculación a eliminar
      final updatedCalculations = currentCalculations
          .where((calc) => calc.id != id)
          .toList();

      // Guardar la lista actualizada
      final calculationsJson = json.encode(
        updatedCalculations.map((calc) => calc.toJson()).toList(),
      );

      return await prefs.setString(_calculationsKey, calculationsJson);
    } catch (e) {
      print('Error deleting calculation: $e');
      return false;
    }
  }

  /// Obtiene una calculación específica por ID
  static Future<SavedCalculation?> getCalculationById(String id) async {
    try {
      final calculations = await getSavedCalculations();
      return calculations.firstWhere(
        (calc) => calc.id == id,
        orElse: () => throw StateError('Calculation not found'),
      );
    } catch (e) {
      print('Error getting calculation by ID: $e');
      return null;
    }
  }

  /// Limpia todas las calculaciones guardadas
  static Future<bool> clearAllCalculations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_calculationsKey);
    } catch (e) {
      print('Error clearing calculations: $e');
      return false;
    }
  }

  /// Actualiza una calculación existente
  static Future<bool> updateCalculation(SavedCalculation updatedCalculation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCalculations = await getSavedCalculations();

      // Encontrar y actualizar la calculación
      final index = currentCalculations.indexWhere(
        (calc) => calc.id == updatedCalculation.id,
      );

      if (index == -1) {
        return false; // Calculación no encontrada
      }

      currentCalculations[index] = updatedCalculation;

      // Guardar la lista actualizada
      final calculationsJson = json.encode(
        currentCalculations.map((calc) => calc.toJson()).toList(),
      );

      return await prefs.setString(_calculationsKey, calculationsJson);
    } catch (e) {
      print('Error updating calculation: $e');
      return false;
    }
  }
}
