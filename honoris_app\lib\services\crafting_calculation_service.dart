import '../models/crafting_product.dart';
import '../models/crafting_calculation.dart';
import 'rental_cost_service.dart';

class CraftingCalculationService {
  /// Calcula el resultado de fabricación basado en la configuración
  static CraftingResult calculateCrafting({
    required CraftingProduct product,
    required CraftingCalculation calculation,
  }) {
    // Calcular ingredientes realmente usados considerando el retorno
    final returnMultiplier = 1 - (calculation.returnPercentage / 100);

    // Costo total de ingredientes (considerando retorno)
    double totalIngredientCost = 0;
    for (final ingredient in product.ingredients) {
      final actualQuantityUsed =
          ingredient.quantity * calculation.batches * returnMultiplier;
      totalIngredientCost += actualQuantityUsed * ingredient.pricePerUnit;
    }

    // Costo de alquiler basado en el valor del objeto
    final totalRentalCost = RentalCostService.calculateRentalCost(
      objectValue: product.objectPower, // Considera renombrar a product.objectValue si lo cambiaste en el modelo
      batches: calculation.batches,
      unitsPerBatch: product.craftedQuantity,
      baseRentalRate: calculation.rentalCost, // Ahora toma el valor ingresado por el usuario
    );

    // Costo total de fabricación
    final totalManufacturingCost = totalIngredientCost + totalRentalCost;

    // Unidades totales producidas
    final totalUnitsProduced = product.craftedQuantity * calculation.batches;

    // Ingresos brutos
    final totalRevenue = totalUnitsProduced * calculation.sellingPricePerUnit;

    // Calcular tasas de venta
    final totalSalesTax =
        calculation.salesPercentage + calculation.taxPercentage;
    final netRevenueMultiplier = 1 - (totalSalesTax / 100);
    final netRevenue = totalRevenue * netRevenueMultiplier;

    // Costo de publicación (2.5% sobre precio de venta)
    final publicationCost = totalRevenue * (calculation.taxPercentage / 100);

    // Ganancia/pérdida neta
    final netProfitLoss = netRevenue - totalManufacturingCost;

    // Porcentaje de ganancia/pérdida
    final profitLossPercentage = totalManufacturingCost > 0
        ? (netProfitLoss / totalManufacturingCost) * 100
        : 0.0;

    // Costo e ingreso por unidad
    final costPerUnit = totalUnitsProduced > 0
        ? totalManufacturingCost / totalUnitsProduced
        : 0.0;
    final revenuePerUnit = netRevenue / totalUnitsProduced;

    // Cantidad real de ingredientes usados (para mostrar en resultados)
    final actualIngredientsUsed = product.ingredients.fold<double>(
      0.0,
      (sum, ingredient) =>
          sum + (ingredient.quantity * calculation.batches * returnMultiplier),
    );

    return CraftingResult(
      totalIngredientCost: double.parse(totalIngredientCost.toStringAsFixed(2)),
      totalRentalCost: double.parse(totalRentalCost.toStringAsFixed(2)),
      totalManufacturingCost: double.parse(
        totalManufacturingCost.toStringAsFixed(2),
      ),
      totalRevenue: double.parse(totalRevenue.toStringAsFixed(2)),
      netRevenue: double.parse(netRevenue.toStringAsFixed(2)),
      publicationCost: double.parse(publicationCost.toStringAsFixed(2)),
      netProfitLoss: double.parse(netProfitLoss.toStringAsFixed(2)),
      profitLossPercentage: double.parse(
        profitLossPercentage.toStringAsFixed(2),
      ),
      costPerUnit: double.parse(costPerUnit.toStringAsFixed(2)),
      revenuePerUnit: double.parse(revenuePerUnit.toStringAsFixed(2)),
      totalUnitsProduced: totalUnitsProduced,
      actualIngredientsUsed: double.parse(
        actualIngredientsUsed.toStringAsFixed(2),
      ),
    );
  }

  /// Valida que se pueda realizar el cálculo
  static List<String> validateCalculation({
    required CraftingProduct product,
    required CraftingCalculation calculation,
    required Map<String, double> availableQuantities,
  }) {
    final errors = <String>[];

    // Validar que hay suficientes ingredientes
    final requiredIngredients = product.getRequiredIngredients(
      calculation.batches,
    );

    for (final entry in requiredIngredients.entries) {
      final itemId = entry.key;
      final required = entry.value;
      final available = availableQuantities[itemId] ?? 0;

      if (available < required) {
        final ingredient = product.ingredients.firstWhere(
          (ing) => ing.itemId == itemId,
        );
        errors.add(
          'No hay suficiente ${ingredient.itemName}. Necesario: ${required.toStringAsFixed(1)}, Disponible: ${available.toStringAsFixed(1)}',
        );
      }
    }

    // Validar configuración básica
    if (calculation.batches <= 0) {
      errors.add('El número de lotes debe ser mayor a 0');
    }

    if (calculation.sellingPricePerUnit <= 0) {
      errors.add('El precio de venta por unidad debe ser mayor a 0');
    }

    if (calculation.rentalCost < 0) {
      errors.add('El costo de alquiler no puede ser negativo');
    }

    if (calculation.returnPercentage < 0 ||
        calculation.returnPercentage > 100) {
      errors.add('El porcentaje de retorno debe estar entre 0 y 100');
    }

    return errors;
  }

  /// Calcula cuántos lotes se pueden fabricar con el inventario disponible
  static int calculateMaxBatches({
    required CraftingProduct product,
    required Map<String, double> availableQuantities,
  }) {
    int maxBatches = 0;
    bool first = true;

    for (final ingredient in product.ingredients) {
      final available = availableQuantities[ingredient.itemId] ?? 0;
      final batchesFromThisIngredient = (available / ingredient.quantity)
          .floor();

      if (first) {
        maxBatches = batchesFromThisIngredient;
        first = false;
      } else {
        maxBatches = maxBatches < batchesFromThisIngredient
            ? maxBatches
            : batchesFromThisIngredient;
      }
    }

    return maxBatches;
  }

  /// Simula el consumo de ingredientes con retorno
  static Map<String, double> simulateIngredientConsumption({
    required CraftingProduct product,
    required int batches,
    required double returnPercentage,
  }) {
    final consumption = <String, double>{};
    final returnMultiplier = 1 - (returnPercentage / 100);

    for (final ingredient in product.ingredients) {
      final totalNeeded = ingredient.quantity * batches;
      final actualConsumed = totalNeeded * returnMultiplier;
      consumption[ingredient.itemId] = actualConsumed;
    }

    return consumption;
  }

  /// Calcula el foco total usado en la fabricación
  static double calculateTotalFocusUsed({
    required CraftingProduct product,
    required int batches,
  }) {
    // Foco de ingredientes
    final ingredientsFocus = product.ingredients.fold<double>(
      0.0,
      (sum, ingredient) => sum + (ingredient.totalFocus * batches),
    );

    // Foco adicional por lote
    final additionalFocus = product.focusPerBatch * batches;

    return ingredientsFocus + additionalFocus;
  }
}
