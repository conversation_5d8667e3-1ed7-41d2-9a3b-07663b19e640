import 'package:flutter/material.dart';
import '../services/inventory_service.dart';
import '../services/market_service.dart';

class BalanceScreen extends StatefulWidget {
  const BalanceScreen({super.key});

  @override
  State<BalanceScreen> createState() => _BalanceScreenState();
}

class _BalanceScreenState extends State<BalanceScreen> {
  bool _isLoading = true;
  double _inventoryCost = 0.0;
  double _marketValue = 0.0;
  double _salesProfit = 0.0;
  double _taxesPaid = 0.0;
  int _inventoryItemCount = 0;
  int _activeListingsCount = 0;
  int _soldListingsCount = 0;

  @override
  void initState() {
    super.initState();
    _loadBalanceData();
  }

  Future<void> _loadBalanceData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Cargar datos del inventario
      final inventoryItems = await InventoryService.getInventoryItems();
      final inventoryCost = inventoryItems.fold<double>(
        0.0,
        (sum, item) => sum + (item.price * item.quantity),
      );

      // Cargar datos del mercado
      final marketValue = await MarketService.getTotalMarketValue();
      final salesProfit = await MarketService.getTotalSalesProfit();
      final taxesPaid = await MarketService.getTotalTaxesPaid();

      // Cargar contadores
      final activeListings = await MarketService.getActiveListings();
      final soldListings = await MarketService.getSoldListings();

      setState(() {
        _inventoryCost = inventoryCost;
        _marketValue = marketValue;
        _salesProfit = salesProfit;
        _taxesPaid = taxesPaid;
        _inventoryItemCount = inventoryItems.length;
        _activeListingsCount = activeListings.length;
        _soldListingsCount = soldListings.length;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error cargando balance: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Balance General'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            onPressed: _loadBalanceData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualizar',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadBalanceData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Resumen general
                    _buildSummaryCard(),
                    const SizedBox(height: 16),
                    
                    // Inventario
                    _buildInventoryCard(),
                    const SizedBox(height: 16),
                    
                    // Mercado
                    _buildMarketCard(),
                    const SizedBox(height: 16),
                    
                    // Ventas
                    _buildSalesCard(),
                    const SizedBox(height: 16),
                    
                    // Análisis
                    _buildAnalysisCard(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSummaryCard() {
    final totalAssets = _inventoryCost + _marketValue;
    final netProfit = _salesProfit - _taxesPaid;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Resumen General',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Activos Totales',
                    '\$${totalAssets.toStringAsFixed(2)}',
                    Icons.account_balance_wallet,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Ganancia Neta',
                    '\$${netProfit.toStringAsFixed(2)}',
                    netProfit >= 0 ? Icons.trending_up : Icons.trending_down,
                    netProfit >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.inventory, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Inventario',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Valor Total', '\$${_inventoryCost.toStringAsFixed(2)}'),
                ),
                Expanded(
                  child: _buildInfoItem('Items', '$_inventoryItemCount'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.store, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Productos en Mercado',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Valor en Mercado', '\$${_marketValue.toStringAsFixed(2)}'),
                ),
                Expanded(
                  child: _buildInfoItem('Publicaciones Activas', '$_activeListingsCount'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monetization_on, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Ventas Realizadas',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Ingresos Netos', '\$${_salesProfit.toStringAsFixed(2)}'),
                ),
                Expanded(
                  child: _buildInfoItem('Impuestos Pagados', '\$${_taxesPaid.toStringAsFixed(2)}'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Ventas Completadas', '$_soldListingsCount'),
                ),
                Expanded(
                  child: _buildInfoItem('Tasa Efectiva', '${_salesProfit > 0 ? ((_taxesPaid / (_salesProfit + _taxesPaid)) * 100).toStringAsFixed(1) : "0.0"}%'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisCard() {
    final totalAssets = _inventoryCost + _marketValue;
    final liquidityRatio = totalAssets > 0 ? (_marketValue / totalAssets) * 100 : 0.0;
    final profitMargin = (_salesProfit + _taxesPaid) > 0 ? (_salesProfit / (_salesProfit + _taxesPaid)) * 100 : 0.0;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Análisis Financiero',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Liquidez', '${liquidityRatio.toStringAsFixed(1)}%'),
                ),
                Expanded(
                  child: _buildInfoItem('Margen de Ganancia', '${profitMargin.toStringAsFixed(1)}%'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Interpretación:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• Liquidez: ${liquidityRatio > 50 ? "Alta" : liquidityRatio > 25 ? "Media" : "Baja"} - ${liquidityRatio.toStringAsFixed(1)}% de activos en el mercado',
                    style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                  ),
                  Text(
                    '• Rentabilidad: ${profitMargin > 70 ? "Excelente" : profitMargin > 50 ? "Buena" : profitMargin > 30 ? "Regular" : "Baja"}',
                    style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
