import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../services/market_service.dart';
import '../services/inventory_service.dart';
import '../services/number_format_service.dart';
import '../models/market_listing.dart';
import '../models/inventory_item.dart';

class ProfitReportsScreen extends StatefulWidget {
  const ProfitReportsScreen({super.key});

  @override
  State<ProfitReportsScreen> createState() => _ProfitReportsScreenState();
}

class _ProfitReportsScreenState extends State<ProfitReportsScreen> {
  bool _isLoading = true;
  int _selectedPeriod = 7; // semanal, mensual, trimestral, semestral, anual
  List<SaleTransaction> _salesData = [];
  List<InventoryItem> _purchasesData = [];
  Map<DateTime, double> _dailySales = {};
  Map<DateTime, double> _dailyPurchases = {};
  Map<DateTime, double> _dailyProfits = {};

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  String _getPeriodDisplayName() {
    switch (_selectedPeriod) {
      case 7:
        return 'Semanal';
      case 30:
        return 'Mensual';
      case 90:
        return 'Trimestral';
      case 180:
        return 'Semestral';
      case 365:
        return 'Anual';
      default:
        return '$_selectedPeriod días';
    }
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Cargar datos de ventas
      final allSales = await MarketService.loadSaleTransactions();

      // Cargar datos de compras (inventario)
      final allPurchases = await InventoryService.getInventoryItems();

      // Filtrar por período seleccionado
      final cutoffDate = DateTime.now().subtract(
        Duration(days: _selectedPeriod),
      );

      _salesData = allSales
          .where((sale) => sale.soldAt.isAfter(cutoffDate))
          .toList();

      _purchasesData = allPurchases
          .where((item) => item.createdAt.isAfter(cutoffDate))
          .toList();

      // Procesar datos para gráficos
      _processDailyData();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error cargando datos: $e')));
      }
    }
  }

  void _processDailyData() {
    _dailySales.clear();
    _dailyPurchases.clear();
    _dailyProfits.clear();

    // Procesar ventas por día
    for (final sale in _salesData) {
      final date = DateTime(
        sale.soldAt.year,
        sale.soldAt.month,
        sale.soldAt.day,
      );
      _dailySales[date] = (_dailySales[date] ?? 0) + sale.netRevenue;
    }

    // Procesar compras por día
    for (final purchase in _purchasesData) {
      final date = DateTime(
        purchase.createdAt.year,
        purchase.createdAt.month,
        purchase.createdAt.day,
      );
      final cost = purchase.quantity * purchase.price;
      _dailyPurchases[date] = (_dailyPurchases[date] ?? 0) + cost;
    }

    // Calcular ganancias diarias
    final allDates = {..._dailySales.keys, ..._dailyPurchases.keys};
    for (final date in allDates) {
      final sales = _dailySales[date] ?? 0;
      final purchases = _dailyPurchases[date] ?? 0;
      _dailyProfits[date] = sales - purchases;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reportes de Ganancias'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          PopupMenuButton<int>(
            onSelected: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadReportData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 7, child: Text('Semanal (7 días)')),
              const PopupMenuItem(value: 30, child: Text('Mensual (30 días)')),
              const PopupMenuItem(
                value: 90,
                child: Text('Trimestral (90 días)'),
              ),
              const PopupMenuItem(
                value: 180,
                child: Text('Semestral (180 días)'),
              ),
              const PopupMenuItem(value: 365, child: Text('Anual (365 días)')),
            ],
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(_getPeriodDisplayName()),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadReportData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Resumen del período
                    _buildSummaryCard(),
                    const SizedBox(height: 16),

                    // Gráfico principal
                    _buildMainChart(),
                    const SizedBox(height: 16),

                    // Estadísticas detalladas
                    _buildDetailedStats(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSummaryCard() {
    final totalSales = _dailySales.values.fold<double>(
      0,
      (sum, value) => sum + value,
    );
    final totalPurchases = _dailyPurchases.values.fold<double>(
      0,
      (sum, value) => sum + value,
    );
    final totalProfit = totalSales - totalPurchases;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Resumen ${_getPeriodDisplayName()}',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Ventas',
                    NumberFormatService.formatCurrency(totalSales),
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Compras',
                    NumberFormatService.formatCurrency(totalPurchases),
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Ganancia',
                    NumberFormatService.formatCurrency(totalProfit),
                    totalProfit >= 0 ? Colors.amber : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildMainChart() {
    if (_dailyProfits.isEmpty) {
      return Card(
        child: Container(
          height: 300,
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: Text('No hay datos para mostrar en este período'),
          ),
        ),
      );
    }

    // Preparar datos para el gráfico
    final sortedDates = _dailyProfits.keys.toList()..sort();
    final salesSpots = <FlSpot>[];
    final purchasesSpots = <FlSpot>[];
    final profitsSpots = <FlSpot>[];

    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final x = i.toDouble();

      salesSpots.add(FlSpot(x, _dailySales[date] ?? 0));
      purchasesSpots.add(FlSpot(x, _dailyPurchases[date] ?? 0));
      profitsSpots.add(FlSpot(x, _dailyProfits[date] ?? 0));
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Evolución Diaria',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            NumberFormatService.formatCurrency(value),
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 &&
                              value.toInt() < sortedDates.length) {
                            final date = sortedDates[value.toInt()];
                            return Text(
                              '${date.day}/${date.month}',
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    // Línea de ventas (verde)
                    LineChartBarData(
                      spots: salesSpots,
                      isCurved: true,
                      color: Colors.green,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                    ),
                    // Línea de compras (rojo)
                    LineChartBarData(
                      spots: purchasesSpots,
                      isCurved: true,
                      color: Colors.red,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                    ),
                    // Línea de ganancias (dorado)
                    LineChartBarData(
                      spots: profitsSpots,
                      isCurved: true,
                      color: Colors.amber,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Leyenda
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildLegendItem('Ventas', Colors.green),
                _buildLegendItem('Compras', Colors.red),
                _buildLegendItem('Ganancias', Colors.amber),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(width: 16, height: 3, color: color),
        const SizedBox(width: 4),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildDetailedStats() {
    final avgDailySales = _dailySales.isNotEmpty
        ? _dailySales.values.reduce((a, b) => a + b) / _dailySales.length
        : 0.0;
    final avgDailyPurchases = _dailyPurchases.isNotEmpty
        ? _dailyPurchases.values.reduce((a, b) => a + b) /
              _dailyPurchases.length
        : 0.0;
    final avgDailyProfit = avgDailySales - avgDailyPurchases;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Estadísticas Detalladas',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Promedio Diario Ventas',
                    NumberFormatService.formatCurrency(avgDailySales),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Promedio Diario Compras',
                    NumberFormatService.formatCurrency(avgDailyPurchases),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Promedio Diario Ganancia',
                    NumberFormatService.formatCurrency(avgDailyProfit),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Transacciones de Venta',
                    '${_salesData.length}',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }
}
