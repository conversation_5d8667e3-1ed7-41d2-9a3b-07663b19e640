/// Modelo para almacenar pronósticos de venta
class SaleForecast {
  final String itemName;
  final double quantity;
  final double cost;
  final double lastSalePrice;
  final double forecastPrice;
  final double profit;
  final double profitPercentage;
  final double focusUsed;
  final double focusEfficiency;
  final DateTime createdAt;

  SaleForecast({
    required this.itemName,
    required this.quantity,
    required this.cost,
    required this.lastSalePrice,
    required this.forecastPrice,
    required this.profit,
    required this.profitPercentage,
    required this.focusUsed,
    required this.focusEfficiency,
    required this.createdAt,
  });

  factory SaleForecast.fromJson(Map<String, dynamic> json) {
    return SaleForecast(
      itemName: json['itemName'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      cost: (json['cost'] as num).toDouble(),
      lastSalePrice: (json['lastSalePrice'] as num).toDouble(),
      forecastPrice: (json['forecastPrice'] as num).toDouble(),
      profit: (json['profit'] as num).toDouble(),
      profitPercentage: (json['profitPercentage'] as num).toDouble(),
      focusUsed: (json['focusUsed'] as num).toDouble(),
      focusEfficiency: (json['focusEfficiency'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'itemName': itemName,
      'quantity': quantity,
      'cost': cost,
      'lastSalePrice': lastSalePrice,
      'forecastPrice': forecastPrice,
      'profit': profit,
      'profitPercentage': profitPercentage,
      'focusUsed': focusUsed,
      'focusEfficiency': focusEfficiency,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  SaleForecast copyWith({
    String? itemName,
    double? quantity,
    double? cost,
    double? lastSalePrice,
    double? forecastPrice,
    double? profit,
    double? profitPercentage,
    double? focusUsed,
    double? focusEfficiency,
    DateTime? createdAt,
  }) {
    return SaleForecast(
      itemName: itemName ?? this.itemName,
      quantity: quantity ?? this.quantity,
      cost: cost ?? this.cost,
      lastSalePrice: lastSalePrice ?? this.lastSalePrice,
      forecastPrice: forecastPrice ?? this.forecastPrice,
      profit: profit ?? this.profit,
      profitPercentage: profitPercentage ?? this.profitPercentage,
      focusUsed: focusUsed ?? this.focusUsed,
      focusEfficiency: focusEfficiency ?? this.focusEfficiency,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// Enumeración para tipos de ordenamiento
enum SortType {
  alphabetical,
  profitPercentage,
  focusEfficiency,
}

extension SortTypeExtension on SortType {
  String get displayName {
    switch (this) {
      case SortType.alphabetical:
        return 'Alfabético';
      case SortType.profitPercentage:
        return '% Ganancia';
      case SortType.focusEfficiency:
        return 'Eficiencia Foco';
    }
  }

  String get description {
    switch (this) {
      case SortType.alphabetical:
        return 'Ordenar por nombre';
      case SortType.profitPercentage:
        return 'Ordenar por % de ganancia';
      case SortType.focusEfficiency:
        return 'Ordenar por ganancia/foco';
    }
  }
}
