import 'package:flutter/material.dart';

class TutorialGuideService extends ChangeNotifier {
  static final TutorialGuideService _instance =
      TutorialGuideService._internal();
  factory TutorialGuideService() => _instance;
  TutorialGuideService._internal();

  bool _isActive = false;
  int _currentStep = 0;
  String _currentScreen = 'home';
  OverlayEntry? _overlayEntry;
  BuildContext? _context;

  bool get isActive => _isActive;
  int get currentStep => _currentStep;
  String get currentScreen => _currentScreen;

  final List<TutorialGuideStep> _steps = [
    // FASE 1: CONFIGURACIÓN INICIAL
    TutorialGuideStep(
      id: 0,
      screen: 'home',
      title: '¡Hoy inicia tu día como empresario exitoso!',
      description:
          'Bienvenido a Albion Online ROI. Hoy aprenderás a crear un imperio comercial completo fabricando "Cerdo Asado" desde cero.',
      instruction:
          'Toca el botón "Tutorial: Ce<PERSON>" para comenzar tu aventura empresarial.',
      targetWidget: 'tutorial_button',
      highlightColor: Colors.orange,
    ),
    TutorialGuideStep(
      id: 1,
      screen: 'tutorial',
      title: 'Tu Imperio: Una Isla Próspera',
      description:
          'Tienes una isla con 2 parcelas de maíz y 1 parcela de cerdos. Los cerdos comen maíz, y necesitarás leche que compraremos en el mercado.',
      instruction:
          'Vamos al inventario para gestionar nuestros recursos. Toca "IR AL INVENTARIO".',
      targetWidget: 'inventory_button',
      highlightColor: Colors.blue,
    ),

    // FASE 2: GESTIÓN DE INVENTARIO (DÍA 1)
    TutorialGuideStep(
      id: 2,
      screen: 'inventory',
      title: 'Día 1: Agregar Cosecha de Maíz',
      description:
          'Primero vamos a registrar la cosecha de maíz de tus parcelas. Este maíz alimentará a los cerdos.',
      instruction:
          'Toca el botón "+" (Agregar) en la parte superior para agregar tu primera cosecha.',
      targetWidget: 'add_button',
      highlightColor: Colors.green,
    ),
    TutorialGuideStep(
      id: 3,
      screen: 'inventory_add',
      title: 'Seleccionar Tipo: Cosecha',
      description:
          'Ahora selecciona el tipo de acción que realizaste en tu isla.',
      instruction: 'Toca "Cosecha" en la lista de opciones disponibles.',
      targetWidget: 'harvest_option',
      highlightColor: Colors.green,
    ),
    TutorialGuideStep(
      id: 4,
      screen: 'harvest_form',
      title: 'Datos de tu Cosecha de Maíz',
      description:
          'Llena los campos con los datos exactos de tu cosecha. Cada semilla costó 26.010 en la isla (0% tasa).',
      instruction:
          'Usa estos valores:\n• Nombre: Maíz\n• Precio semilla: 26010\n• Semillas compradas: 18\n• Semillas devueltas: 15\n• Cosecha recibida: 152\n• Focus por semilla: 0,00',
      targetWidget: 'harvest_form',
      highlightColor: Colors.green,
      data: {
        'Nombre del Cultivo': 'Maíz',
        'Precio de la Semilla': '26010',
        'Semillas Compradas': '18',
        'Semillas Recibidas': '15',
        'Cosecha Recibida': '152',
        'Foco por Semilla': '0,00',
      },
    ),
    TutorialGuideStep(
      id: 5,
      screen: 'inventory',
      title: 'Costo del Maíz: 513,36 por unidad',
      description:
          'Perfecto! Cada grano de maíz te costó 513,36. Este maíz servirá para alimentar a los cerdos y crear tu imperio.',
      instruction:
          'Ahora agreguemos los lechones. Toca el botón "+" nuevamente.',
      targetWidget: 'add_button',
      highlightColor: Colors.blue,
    ),
    TutorialGuideStep(
      id: 6,
      screen: 'inventory_add',
      title: 'Seleccionar Tipo: Cría de Animales',
      description:
          'Ahora vamos a registrar la cría de lechones que compraste ayer.',
      instruction: 'Toca "Cría de Animales" en la lista de opciones.',
      targetWidget: 'breeding_option',
      highlightColor: Colors.blue,
    ),
    TutorialGuideStep(
      id: 7,
      screen: 'breeding_form',
      title: 'Datos de tu Cría de Lechones',
      description:
          'Compraste 9 lechones ayer por 26.010 cada uno, pero solo nacieron 8 crías. Se alimentan con el maíz de tu inventario.',
      instruction:
          'Usa estos valores:\n• Animal: Lechón\n• Precio cría: 26010\n• Crías compradas: 9\n• Crías nacidas: 8\n• Alimento: Maíz\n• Alimento por día: 9\n• Tiempo crecimiento: 24',
      targetWidget: 'breeding_form',
      highlightColor: Colors.blue,
      data: {
        'Nombre del Animal': 'Lechón',
        'Precio de la Cría': '26010',
        'Crías Compradas': '9',
        'Crías que Nacieron': '8',
        'Alimento por Día': '9',
        'Tiempo de Crecimiento': '24',
      },
    ),
    TutorialGuideStep(
      id: 8,
      screen: 'inventory',
      title: 'Comprar Leche en el Mercado',
      description:
          'Necesitamos leche para la receta del cerdo asado. Vamos a comprarla directamente del mercado.',
      instruction:
          'Toca el botón "+" una vez más para hacer una compra directa.',
      targetWidget: 'add_button',
      highlightColor: Colors.purple,
    ),
    TutorialGuideStep(
      id: 9,
      screen: 'inventory_add',
      title: 'Seleccionar Tipo: Compra Directa',
      description:
          'Esta vez vamos a registrar una compra que hiciste en el mercado.',
      instruction: 'Toca "Compra Directa" en la lista de opciones.',
      targetWidget: 'direct_purchase_option',
      highlightColor: Colors.purple,
    ),
    TutorialGuideStep(
      id: 10,
      screen: 'direct_purchase_form',
      title: 'Datos de Compra de Leche',
      description:
          'Compraste 360 unidades de leche de vaca en el mercado a 275 por unidad, pagando 2.5% de tasa como premium.',
      instruction:
          'Usa estos valores:\n• Nombre: Leche de Vaca\n• Cantidad: 360\n• Precio por unidad: 275\n• Marcar: Comprado en el Mercado (2.5% tasa)',
      targetWidget: 'direct_purchase_form',
      highlightColor: Colors.purple,
      data: {
        'Nombre del Artículo': 'Leche de Vaca',
        'Cantidad': '360',
        'Precio por Unidad': '275',
        'Comprado en el Mercado': 'Sí (2.5% tasa)',
      },
    ),
  ];

  void startTutorial(BuildContext context) {
    _context = context;
    _isActive = true;
    _currentStep = 0;
    _currentScreen = 'home';
    notifyListeners();
    _showCurrentStep();
  }

  void nextStep() {
    if (_currentStep < _steps.length - 1) {
      _currentStep++;
      notifyListeners();
      _showCurrentStep();
    } else {
      completeTutorial();
    }
  }

  void updateScreen(String screenName) {
    _currentScreen = screenName;
    notifyListeners();

    // Auto-avanzar si llegamos a la pantalla correcta
    final currentStepData = _steps[_currentStep];
    if (currentStepData.screen == screenName) {
      _showCurrentStep();
    }
  }

  void completeTutorial() {
    _isActive = false;
    _hideOverlay();
    notifyListeners();

    if (_context != null) {
      showDialog(
        context: _context!,
        builder: (context) => AlertDialog(
          title: const Text('¡Tutorial Completado!'),
          content: const Text(
            '¡El mundo de Albion es tuyo!\n\nINICIA TU IMPERIO COMERCIAL YA!\n\nPuedes usar el botón "Restablecer Balance" para empezar de nuevo.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('FINALIZAR'),
            ),
          ],
        ),
      );
    }
  }

  void _showCurrentStep() {
    _hideOverlay();

    if (_context == null || !_isActive) return;

    final step = _steps[_currentStep];

    _overlayEntry = OverlayEntry(
      builder: (context) => TutorialOverlay(
        step: step,
        onNext: nextStep,
        onSkip: completeTutorial,
      ),
    );

    Overlay.of(_context!).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  TutorialGuideStep? getCurrentStep() {
    if (!_isActive || _currentStep >= _steps.length) return null;
    return _steps[_currentStep];
  }

  void dispose() {
    _hideOverlay();
    super.dispose();
  }
}

class TutorialGuideStep {
  final int id;
  final String screen;
  final String title;
  final String description;
  final String instruction;
  final String targetWidget;
  final Color highlightColor;
  final Map<String, dynamic>? data;

  TutorialGuideStep({
    required this.id,
    required this.screen,
    required this.title,
    required this.description,
    required this.instruction,
    required this.targetWidget,
    required this.highlightColor,
    this.data,
  });
}

class TutorialOverlay extends StatelessWidget {
  final TutorialGuideStep step;
  final VoidCallback onNext;
  final VoidCallback onSkip;

  const TutorialOverlay({
    super.key,
    required this.step,
    required this.onNext,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.7),
      child: Stack(
        children: [
          // Fondo semi-transparente
          Positioned.fill(
            child: GestureDetector(
              onTap: () {}, // Prevenir toques en el fondo
            ),
          ),

          // Contenido del tutorial
          Positioned(
            bottom: 100,
            left: 16,
            right: 16,
            child: Card(
              elevation: 8,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Título
                    Text(
                      step.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: step.highlightColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),

                    // Descripción
                    Text(
                      step.description,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    // Instrucción
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: step.highlightColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: step.highlightColor),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.touch_app,
                            color: step.highlightColor,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            step.instruction,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: step.highlightColor,
                                ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Datos específicos si los hay
                    if (step.data != null) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'DATOS PARA USAR:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...step.data!.entries
                                .map(
                                  (entry) => Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 1,
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '${entry.key}:',
                                          style: const TextStyle(fontSize: 11),
                                        ),
                                        Text(
                                          entry.value.toString(),
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 11,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                                .toList(),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // Botones
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: onSkip,
                            child: const Text('SALIR'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: onNext,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: step.highlightColor,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('ENTENDIDO'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
