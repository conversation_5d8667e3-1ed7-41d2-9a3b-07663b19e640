import 'package:flutter/material.dart';

class TutorialGuideService extends ChangeNotifier {
  static final TutorialGuideService _instance = TutorialGuideService._internal();
  factory TutorialGuideService() => _instance;
  TutorialGuideService._internal();

  bool _isActive = false;
  int _currentStep = 0;
  String _currentScreen = 'home';
  OverlayEntry? _overlayEntry;
  BuildContext? _context;

  bool get isActive => _isActive;
  int get currentStep => _currentStep;
  String get currentScreen => _currentScreen;

  final List<TutorialGuideStep> _steps = [
    TutorialGuideStep(
      id: 0,
      screen: 'home',
      title: '¡Bienvenido!',
      description: 'Vamos a crear un imperio comercial completo. Comenzaremos fabricando "Cerdo Asado".',
      instruction: 'Toca el botón "Tutorial: Cerdo Asado" para comenzar.',
      targetWidget: 'tutorial_button',
      highlightColor: Colors.orange,
    ),
    TutorialGuideStep(
      id: 1,
      screen: 'tutorial',
      title: 'Ir al Inventario',
      description: 'Necesitamos gestionar nuestros materiales. El inventario es donde guardamos todo.',
      instruction: 'Toca "IR AL INVENTARIO" para continuar.',
      targetWidget: 'inventory_button',
      highlightColor: Colors.blue,
    ),
    TutorialGuideStep(
      id: 2,
      screen: 'inventory',
      title: 'Agregar Cosecha',
      description: 'Vamos a simular que cosechaste maíz en tu isla.',
      instruction: 'Toca el botón "+" (Agregar) en la parte superior.',
      targetWidget: 'add_button',
      highlightColor: Colors.green,
      data: {
        'itemName': 'Maíz',
        'seedsPurchased': 26010,
        'seedsReturned': 18,
        'harvestReceived': 152,
        'seedPrice': 50.0,
        'focusPerSeed': 0.1,
      },
    ),
    TutorialGuideStep(
      id: 3,
      screen: 'inventory_add',
      title: 'Seleccionar Cosecha',
      description: 'Ahora selecciona el tipo de acción que quieres realizar.',
      instruction: 'Toca "Cosecha" en la lista de opciones.',
      targetWidget: 'harvest_option',
      highlightColor: Colors.green,
    ),
    TutorialGuideStep(
      id: 4,
      screen: 'harvest_form',
      title: 'Llenar Datos de Cosecha',
      description: 'Llena los campos con los datos de tu cosecha de maíz.',
      instruction: 'Usa estos valores:\n• Nombre: Maíz\n• Semillas compradas: 26.010\n• Semillas devueltas: 18\n• Cosecha recibida: 152\n• Precio semilla: 50,00\n• Focus por semilla: 0,1',
      targetWidget: 'harvest_form',
      highlightColor: Colors.green,
    ),
    TutorialGuideStep(
      id: 5,
      screen: 'inventory',
      title: 'Agregar Cría',
      description: 'Ahora vamos a criar cerdos usando el maíz como comida.',
      instruction: 'Toca el botón "+" nuevamente.',
      targetWidget: 'add_button',
      highlightColor: Colors.blue,
    ),
    TutorialGuideStep(
      id: 6,
      screen: 'inventory_add',
      title: 'Seleccionar Cría',
      description: 'Selecciona la opción para criar animales.',
      instruction: 'Toca "Cría de Animales" en la lista.',
      targetWidget: 'breeding_option',
      highlightColor: Colors.blue,
    ),
    TutorialGuideStep(
      id: 7,
      screen: 'breeding_form',
      title: 'Llenar Datos de Cría',
      description: 'Llena los campos para criar cerdos.',
      instruction: 'Usa estos valores:\n• Animal: Cerdo\n• Animales nacidos: 8\n• Comida por día: 2,0\n• Tiempo crecimiento: 22,0\n• Precio por unidad: 1.500,00',
      targetWidget: 'breeding_form',
      highlightColor: Colors.blue,
    ),
  ];

  void startTutorial(BuildContext context) {
    _context = context;
    _isActive = true;
    _currentStep = 0;
    _currentScreen = 'home';
    notifyListeners();
    _showCurrentStep();
  }

  void nextStep() {
    if (_currentStep < _steps.length - 1) {
      _currentStep++;
      notifyListeners();
      _showCurrentStep();
    } else {
      completeTutorial();
    }
  }

  void updateScreen(String screenName) {
    _currentScreen = screenName;
    notifyListeners();
    
    // Auto-avanzar si llegamos a la pantalla correcta
    final currentStepData = _steps[_currentStep];
    if (currentStepData.screen == screenName) {
      _showCurrentStep();
    }
  }

  void completeTutorial() {
    _isActive = false;
    _hideOverlay();
    notifyListeners();
    
    if (_context != null) {
      showDialog(
        context: _context!,
        builder: (context) => AlertDialog(
          title: const Text('¡Tutorial Completado!'),
          content: const Text(
            '¡El mundo de Albion es tuyo!\n\nINICIA TU IMPERIO COMERCIAL YA!\n\nPuedes usar el botón "Restablecer Balance" para empezar de nuevo.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('FINALIZAR'),
            ),
          ],
        ),
      );
    }
  }

  void _showCurrentStep() {
    _hideOverlay();
    
    if (_context == null || !_isActive) return;
    
    final step = _steps[_currentStep];
    
    _overlayEntry = OverlayEntry(
      builder: (context) => TutorialOverlay(
        step: step,
        onNext: nextStep,
        onSkip: completeTutorial,
      ),
    );
    
    Overlay.of(_context!).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  TutorialGuideStep? getCurrentStep() {
    if (!_isActive || _currentStep >= _steps.length) return null;
    return _steps[_currentStep];
  }

  void dispose() {
    _hideOverlay();
    super.dispose();
  }
}

class TutorialGuideStep {
  final int id;
  final String screen;
  final String title;
  final String description;
  final String instruction;
  final String targetWidget;
  final Color highlightColor;
  final Map<String, dynamic>? data;

  TutorialGuideStep({
    required this.id,
    required this.screen,
    required this.title,
    required this.description,
    required this.instruction,
    required this.targetWidget,
    required this.highlightColor,
    this.data,
  });
}

class TutorialOverlay extends StatelessWidget {
  final TutorialGuideStep step;
  final VoidCallback onNext;
  final VoidCallback onSkip;

  const TutorialOverlay({
    super.key,
    required this.step,
    required this.onNext,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.7),
      child: Stack(
        children: [
          // Fondo semi-transparente
          Positioned.fill(
            child: GestureDetector(
              onTap: () {}, // Prevenir toques en el fondo
            ),
          ),
          
          // Contenido del tutorial
          Positioned(
            bottom: 100,
            left: 16,
            right: 16,
            child: Card(
              elevation: 8,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Título
                    Text(
                      step.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: step.highlightColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    
                    // Descripción
                    Text(
                      step.description,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    
                    // Instrucción
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: step.highlightColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: step.highlightColor),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.touch_app,
                            color: step.highlightColor,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            step.instruction,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: step.highlightColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    
                    // Datos específicos si los hay
                    if (step.data != null) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'DATOS PARA USAR:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...step.data!.entries.map((entry) => 
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 1),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '${entry.key}:',
                                      style: const TextStyle(fontSize: 11),
                                    ),
                                    Text(
                                      entry.value.toString(),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 11,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ).toList(),
                          ],
                        ),
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    
                    // Botones
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: onSkip,
                            child: const Text('SALIR'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: onNext,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: step.highlightColor,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('ENTENDIDO'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
