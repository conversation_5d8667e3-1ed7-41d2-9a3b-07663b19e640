^E:\HERD\HONORIS_OLD\HONORIS_APP\BUILD\WINDOWS\X64\CMAKEFILES\A36B74792BF34A9438A80E7EF4C07915\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/herd/honoris_old/honoris_app/windows -BE:/herd/honoris_old/honoris_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/herd/honoris_old/honoris_app/build/windows/x64/honoris_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
