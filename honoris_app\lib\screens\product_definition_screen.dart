import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/crafting_product.dart';
import '../models/ingredient.dart';
import '../models/inventory_item.dart';
import '../services/number_format_service.dart';
import '../services/tutorial_service.dart';

class ProductDefinitionScreen extends StatefulWidget {
  final CraftingProduct? existingProduct;

  const ProductDefinitionScreen({super.key, this.existingProduct});

  @override
  State<ProductDefinitionScreen> createState() =>
      _ProductDefinitionScreenState();
}

class _ProductDefinitionScreenState extends State<ProductDefinitionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _productNameController = TextEditingController();
  final _objectPowerController = TextEditingController();
  final _craftedQuantityController = TextEditingController();
  final _focusPerBatchController = TextEditingController(text: '0');

  bool _isRawMaterial = false;
  bool _isLoading = false;
  List<Ingredient> _ingredients = [];
  List<InventoryItem> _availableIngredients = [];

  @override
  void initState() {
    super.initState();
    _loadAvailableIngredients();

    if (widget.existingProduct != null) {
      _loadExistingProduct();
    }
  }

  @override
  void dispose() {
    _productNameController.dispose();
    _objectPowerController.dispose();
    _craftedQuantityController.dispose();
    _focusPerBatchController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableIngredients() async {
    final provider = context.read<CraftingProvider>();
    await provider.loadData();

    setState(() {
      // Cargar materias primas del inventario
      _availableIngredients = provider.rawMaterials;

      // Agregar productos fabricados que están físicamente en el inventario
      // y que están marcados como materia prima
      final craftedProducts = provider.craftedItems.where((item) {
        // Verificar si existe un producto de fabricación correspondiente marcado como materia prima
        return provider.craftingProducts.any(
          (product) =>
              product.name == item.name &&
              product.isRawMaterial &&
              item.quantity > 0, // Solo si hay cantidad disponible
        );
      });

      _availableIngredients.addAll(craftedProducts);
    });
  }

  void _loadExistingProduct() {
    final product = widget.existingProduct!;
    _productNameController.text = product.name;
    _objectPowerController.text = product.objectPower.toString();
    _craftedQuantityController.text = product.craftedQuantity.toString();
    _focusPerBatchController.text = product.focusPerBatch.toString();
    _isRawMaterial = product.isRawMaterial;
    _ingredients = List.from(product.ingredients);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.existingProduct == null
              ? 'Definir Producto'
              : 'Editar Producto',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          if (widget.existingProduct != null)
            IconButton(
              onPressed: _deleteProduct,
              icon: const Icon(Icons.delete),
              tooltip: 'Eliminar Producto',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Información del Producto',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        if (widget.existingProduct ==
                            null) // Solo mostrar en productos nuevos
                          ElevatedButton.icon(
                            onPressed: _autoFillTutorialData,
                            icon: const Icon(Icons.auto_fix_high, size: 16),
                            label: const Text('Tutorial'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _productNameController,
                      decoration: const InputDecoration(
                        labelText: 'Nombre del Producto',
                        hintText: 'Ej: Invisibilidad, Cerdo Asado, etc.',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'El nombre del producto es requerido';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _objectPowerController,
                            decoration: const InputDecoration(
                              labelText: 'Valor del Objeto',
                              hintText: '1441',
                              border: OutlineInputBorder(),
                              helperText: 'Para calcular costo de tienda',
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final power = int.tryParse(value);
                              if (power == null || power <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _craftedQuantityController,
                            decoration: const InputDecoration(
                              labelText: 'Cantidad Crafteada',
                              hintText: '5',
                              border: OutlineInputBorder(),
                              helperText: 'Unidades por lote',
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final quantity = int.tryParse(value);
                              if (quantity == null || quantity <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _focusPerBatchController,
                      decoration: const InputDecoration(
                        labelText: 'Foco por Lote (Opcional)',
                        hintText: '0',
                        border: OutlineInputBorder(),
                        helperText: 'Foco adicional para fabricar un lote',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final focus = double.tryParse(value);
                          if (focus == null || focus < 0) {
                            return 'El foco no puede ser negativo';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CheckboxListTile(
                      title: const Text('Es Materia Prima'),
                      subtitle: const Text(
                        'Puede ser usado como ingrediente de otros productos',
                      ),
                      value: _isRawMaterial,
                      onChanged: (value) {
                        setState(() {
                          _isRawMaterial = value ?? false;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Ingredientes (${_ingredients.length})',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _addIngredient,
                          icon: const Icon(Icons.add),
                          label: const Text('Agregar'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    if (_ingredients.isEmpty)
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: const Center(
                          child: Column(
                            children: [
                              Icon(
                                Icons.inventory,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'No hay ingredientes',
                                style: TextStyle(color: Colors.grey),
                              ),
                              Text(
                                'Agrega ingredientes para crear el producto',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      ...List.generate(_ingredients.length, (index) {
                        final ingredient = _ingredients[index];
                        return _buildIngredientCard(ingredient, index);
                      }),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Vista previa de costos
            _buildCostPreview(),

            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveProduct,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : Text(
                        widget.existingProduct == null
                            ? 'Guardar Producto'
                            : 'Actualizar Producto',
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientCard(Ingredient ingredient, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ingredient.itemName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Cantidad: ${NumberFormatService.formatNumber(ingredient.quantity, decimals: 2)} | '
                    'Precio: ${NumberFormatService.formatCurrency(ingredient.pricePerUnit)} | '
                    'Foco: ${NumberFormatService.formatNumber(ingredient.focusPerUnit, decimals: 2)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    'Total: ${NumberFormatService.formatCurrency(ingredient.totalCost)} | '
                    'Foco Total: ${NumberFormatService.formatNumber(ingredient.totalFocus, decimals: 2)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _editIngredient(index),
              icon: const Icon(Icons.edit),
              tooltip: 'Editar',
            ),
            IconButton(
              onPressed: () => _removeIngredient(index),
              icon: const Icon(Icons.delete),
              color: Theme.of(context).colorScheme.error,
              tooltip: 'Eliminar',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostPreview() {
    if (_ingredients.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalCost = _ingredients.fold<double>(
      0,
      (sum, ing) => sum + ing.totalCost,
    );
    final totalFocus = _ingredients.fold<double>(
      0,
      (sum, ing) => sum + ing.totalFocus,
    );
    final focusPerBatch = double.tryParse(_focusPerBatchController.text) ?? 0;
    final craftedQuantity = int.tryParse(_craftedQuantityController.text) ?? 1;

    final costPerUnit = totalCost / craftedQuantity;
    final focusPerUnit = (totalFocus + focusPerBatch) / craftedQuantity;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vista Previa de Costos',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildPreviewItem(
                    'Costo Total por Lote',
                    NumberFormatService.formatCurrency(totalCost),
                  ),
                ),
                Expanded(
                  child: _buildPreviewItem(
                    'Foco Total por Lote',
                    NumberFormatService.formatNumber(
                      (totalFocus + focusPerBatch),
                      decimals: 2,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildPreviewItem(
                    'Costo por Unidad',
                    NumberFormatService.formatCurrency(costPerUnit),
                  ),
                ),
                Expanded(
                  child: _buildPreviewItem(
                    'Foco por Unidad',
                    NumberFormatService.formatNumber(focusPerUnit, decimals: 2),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  void _addIngredient() {
    _showIngredientDialog();
  }

  void _editIngredient(int index) {
    _showIngredientDialog(
      existingIngredient: _ingredients[index],
      index: index,
    );
  }

  void _removeIngredient(int index) {
    setState(() {
      _ingredients.removeAt(index);
    });
  }

  void _showIngredientDialog({Ingredient? existingIngredient, int? index}) {
    showDialog(
      context: context,
      builder: (context) => _IngredientDialog(
        availableIngredients: _availableIngredients,
        existingIngredient: existingIngredient,
        onSave: (ingredient) {
          setState(() {
            if (index != null) {
              _ingredients[index] = ingredient;
            } else {
              _ingredients.add(ingredient);
            }
          });
        },
      ),
    );
  }

  void _autoFillTutorialData() {
    // Datos del tutorial para carne de cerdo (primer producto)
    _productNameController.text = 'Carne de Cerdo';
    _objectPowerController.text = '40';
    _craftedQuantityController.text = '18';
    _focusPerBatchController.text = '0';
    setState(() {
      _isRawMaterial = false;
    });

    // Agregar ingredientes automáticamente
    _ingredients.clear();

    // Buscar lechón en el inventario
    final lechonItem = _availableIngredients.firstWhere(
      (item) =>
          item.name.toLowerCase().contains('lechón') ||
          item.name.toLowerCase().contains('lechon'),
      orElse: () => _availableIngredients.first, // Fallback al primer item
    );

    _ingredients.add(
      Ingredient(
        itemId: lechonItem.id,
        itemName: lechonItem.name,
        quantity: 9,
        pricePerUnit: lechonItem.price,
        focusPerUnit: lechonItem.focusUsed,
      ),
    );

    // Mostrar explicación del tutorial
    TutorialService.showTutorialMessage(
      context,
      'Paso 4: Procesar Carne - Datos Explicados',
      '📊 EXPLICACIÓN DE LOS DATOS:\n\n'
          '🥩 Producto: Carne de Cerdo\n'
          '⚡ Poder del Objeto: 40\n'
          '📦 Cantidad Fabricada: 18 unidades\n'
          '🐷 Ingrediente: 9 lechones (43.5% retorno)\n'
          '🎯 Foco: 0 (no requiere foco)\n\n'
          '💡 ¿Por qué estos números?\n'
          'Procesamos los 9 lechones que criamos para obtener 18 unidades de carne. '
          'El retorno del 43.5% significa que recuperamos algunos lechones.\n\n'
          '⚠️ IMPORTANTE: Ahora debes hacer clic en "Guardar Producto" para continuar.',
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_ingredients.isEmpty) {
      _showErrorDialog('Debes agregar al menos un ingrediente');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final product = CraftingProduct(
        id:
            widget.existingProduct?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _productNameController.text.trim(),
        objectPower: int.parse(_objectPowerController.text),
        craftedQuantity: int.parse(_craftedQuantityController.text),
        ingredients: _ingredients,
        focusPerBatch: double.tryParse(_focusPerBatchController.text) ?? 0,
        isRawMaterial: _isRawMaterial,
        createdAt: widget.existingProduct?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final provider = context.read<CraftingProvider>();
      final success = await provider.saveCraftingProduct(product);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.existingProduct == null
                    ? 'Producto creado exitosamente'
                    : 'Producto actualizado exitosamente',
              ),
              backgroundColor: Colors.green,
            ),
          );

          // Continuar el tutorial si está activo y es un producto nuevo
          if (widget.existingProduct == null) {
            TutorialService.continueAfterSave(context, 'product');
          }

          Navigator.of(context).pop();
        } else {
          _showErrorDialog('Error al guardar el producto');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error inesperado: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteProduct() async {
    if (widget.existingProduct == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Producto'),
        content: Text(
          '¿Estás seguro de que quieres eliminar "${widget.existingProduct!.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );

    if (result == true) {
      final provider = context.read<CraftingProvider>();
      final success = await provider.deleteCraftingProduct(
        widget.existingProduct!.id,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Producto eliminado exitosamente'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          _showErrorDialog('Error al eliminar el producto');
        }
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class _IngredientDialog extends StatefulWidget {
  final List<InventoryItem> availableIngredients;
  final Ingredient? existingIngredient;
  final Function(Ingredient) onSave;

  const _IngredientDialog({
    required this.availableIngredients,
    required this.onSave,
    this.existingIngredient,
  });

  @override
  State<_IngredientDialog> createState() => _IngredientDialogState();
}

class _IngredientDialogState extends State<_IngredientDialog> {
  final _quantityController = TextEditingController();
  InventoryItem? _selectedItem;

  @override
  void initState() {
    super.initState();

    if (widget.existingIngredient != null) {
      final existing = widget.existingIngredient!;
      _quantityController.text = existing.quantity.toString();
      _selectedItem = widget.availableIngredients.firstWhere(
        (item) => item.id == existing.itemId,
        orElse: () => InventoryItem(
          id: existing.itemId,
          name: existing.itemName,
          price: existing.pricePerUnit,
          quantity: 0,
          focusUsed: existing.focusPerUnit,
          origin: ItemOrigin.crafted,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isRawMaterial: false,
        ),
      );
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.existingIngredient == null
            ? 'Agregar Ingrediente'
            : 'Editar Ingrediente',
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<InventoryItem>(
            value: _selectedItem,
            decoration: const InputDecoration(
              labelText: 'Ingrediente',
              border: OutlineInputBorder(),
            ),
            items: widget.availableIngredients.map((item) {
              return DropdownMenuItem(
                value: item,
                child: Text(
                  '${item.name} (${NumberFormatService.formatCurrency(item.price)})',
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedItem = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Selecciona un ingrediente';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _quantityController,
            decoration: const InputDecoration(
              labelText: 'Cantidad Necesaria',
              hintText: '0.0',
              border: OutlineInputBorder(),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'La cantidad es requerida';
              }
              final quantity = double.tryParse(value);
              if (quantity == null || quantity <= 0) {
                return 'Debe ser mayor a 0';
              }
              return null;
            },
          ),

          if (_selectedItem != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Información del Ingrediente',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Precio: ${NumberFormatService.formatCurrency(_selectedItem!.price)} por unidad',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  Text(
                    'Foco: ${NumberFormatService.formatNumber(_selectedItem!.focusUsed, decimals: 2)} por unidad',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  if (_quantityController.text.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Costo Total: ${NumberFormatService.formatCurrency(((double.tryParse(_quantityController.text) ?? 0) * _selectedItem!.price))}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _saveIngredient,
          child: const Text('Guardar'),
        ),
      ],
    );
  }

  void _saveIngredient() {
    if (_selectedItem == null || _quantityController.text.isEmpty) {
      return;
    }

    final quantity = double.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      return;
    }

    final ingredient = Ingredient(
      itemId: _selectedItem!.id,
      itemName: _selectedItem!.name,
      quantity: quantity,
      pricePerUnit: _selectedItem!.price,
      focusPerUnit: _selectedItem!.focusUsed,
    );

    widget.onSave(ingredient);
    Navigator.of(context).pop();
  }
}
