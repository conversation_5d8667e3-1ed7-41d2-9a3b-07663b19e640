{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\herd\\honoris_old\\honoris_app\\build\\.cxx\\RelWithDebInfo\\4b2a6w1v\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\herd\\honoris_old\\honoris_app\\build\\.cxx\\RelWithDebInfo\\4b2a6w1v\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}