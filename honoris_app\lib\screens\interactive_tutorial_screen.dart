import 'package:flutter/material.dart';
import '../services/number_format_service.dart';
import 'inventory_screen.dart';

class InteractiveTutorialScreen extends StatefulWidget {
  const InteractiveTutorialScreen({super.key});

  @override
  State<InteractiveTutorialScreen> createState() => _InteractiveTutorialScreenState();
}

class _InteractiveTutorialScreenState extends State<InteractiveTutorialScreen> {
  int currentStep = 0;
  bool isCompleted = false;

  final List<TutorialStep> steps = [
    TutorialStep(
      title: "¡Bienvenido al Tutorial!",
      description: "Aprenderás a crear un imperio comercial completo desde cero. Vamos a fabricar 'Cerdo Asado' paso a paso.",
      instruction: "Toca 'Siguiente' para comenzar tu aventura comercial.",
      actionType: TutorialActionType.introduction,
    ),
    TutorialStep(
      title: "Paso 1: Ir al Inventario",
      description: "Primero necesitamos gestionar nuestros materiales. El inventario es donde guardamos todo lo que tenemos.",
      instruction: "Toca el botón 'IR AL INVENTARIO' para continuar.",
      actionType: TutorialActionType.navigation,
      targetScreen: "inventory",
    ),
    TutorialStep(
      title: "Paso 2: Agregar Cosecha",
      description: "Vamos a simular que cosechaste maíz en tu isla. Esto será comida para los cerdos.",
      instruction: "En el inventario, toca el botón '+' (Agregar) y selecciona 'Cosecha'.",
      actionType: TutorialActionType.addHarvest,
      data: {
        'itemName': 'Maíz',
        'seedsPurchased': 26010,
        'seedsReturned': 18,
        'harvestReceived': 152,
        'seedPrice': 50.0,
        'focusPerSeed': 0.1,
      },
    ),
    TutorialStep(
      title: "Paso 3: Agregar Cría",
      description: "Ahora vamos a criar cerdos usando el maíz como comida.",
      instruction: "Toca el botón '+' nuevamente y selecciona 'Cría de Animales'.",
      actionType: TutorialActionType.addBreeding,
      data: {
        'animalName': 'Cerdo',
        'animalsBorn': 8,
        'foodPerDay': 2.0,
        'growthTime': 22.0,
        'pricePerUnit': 1500.0,
      },
    ),
    TutorialStep(
      title: "Paso 4: Comprar Leche",
      description: "Necesitamos leche para la receta del cerdo asado. Vamos a comprarla en el mercado.",
      instruction: "Toca '+' y selecciona 'Compra Directa'.",
      actionType: TutorialActionType.directPurchase,
      data: {
        'itemName': 'Leche',
        'quantity': 10,
        'pricePerUnit': 275.0,
        'hasPremium': true,
      },
    ),
    TutorialStep(
      title: "Paso 5: Crear Producto",
      description: "Ahora vamos a definir la receta del 'Cerdo Asado' con todos sus ingredientes.",
      instruction: "Ve a la sección 'Productos' y crea un nuevo producto.",
      actionType: TutorialActionType.createProduct,
      data: {
        'productName': 'Cerdo Asado',
        'objectPower': 576,
        'craftedQuantity': 10,
        'focusUsed': 117.0,
        'isRawMaterial': false,
        'ingredients': [
          {
            'itemId': 'cerdo',
            'itemName': 'Cerdo',
            'quantity': 8.0,
            'pricePerUnit': 1500.0,
            'focusPerUnit': 0.0,
          },
          {
            'itemId': 'leche',
            'itemName': 'Leche',
            'quantity': 10.0,
            'pricePerUnit': 275.0,
            'focusPerUnit': 0.0,
          },
        ],
      },
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final step = steps[currentStep];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tutorial Interactivo'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.orange.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Progreso
                LinearProgressIndicator(
                  value: (currentStep + 1) / steps.length,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),
                const SizedBox(height: 8),
                Text(
                  'Paso ${currentStep + 1} de ${steps.length}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Contenido del paso
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Título
                          Text(
                            step.title,
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),

                          // Descripción
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.orange[50],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.orange[200]!),
                            ),
                            child: Text(
                              step.description,
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                height: 1.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Instrucción específica
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.blue[200]!),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.touch_app,
                                  color: Colors.blue[700],
                                  size: 32,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'INSTRUCCIÓN:',
                                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue[700],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  step.instruction,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: Colors.blue[800],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),

                          // Datos específicos si los hay
                          if (step.data != null) ...[
                            const SizedBox(height: 16),
                            _buildDataDisplay(step.data!),
                          ],

                          const Spacer(),

                          // Botón de acción
                          _buildActionButton(step),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDataDisplay(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'DATOS PARA LLENAR:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 8),
          ...data.entries.map((entry) {
            if (entry.value is List) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${entry.key}:', style: const TextStyle(fontWeight: FontWeight.w500)),
                  ...(entry.value as List).map((item) => 
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Text('• ${item.toString()}'),
                    ),
                  ),
                ],
              );
            }
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${entry.key}:', style: const TextStyle(fontWeight: FontWeight.w500)),
                  Text(
                    entry.value is double 
                        ? NumberFormatService.formatCurrency(entry.value)
                        : entry.value.toString(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildActionButton(TutorialStep step) {
    switch (step.actionType) {
      case TutorialActionType.introduction:
        return ElevatedButton.icon(
          onPressed: _nextStep,
          icon: const Icon(Icons.arrow_forward),
          label: const Text('COMENZAR TUTORIAL'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(16),
          ),
        );
      
      case TutorialActionType.navigation:
        return ElevatedButton.icon(
          onPressed: () => _navigateToScreen(step.targetScreen!),
          icon: const Icon(Icons.navigation),
          label: const Text('IR AL INVENTARIO'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(16),
          ),
        );
      
      default:
        return ElevatedButton.icon(
          onPressed: _nextStep,
          icon: const Icon(Icons.check),
          label: const Text('COMPLETAR PASO'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(16),
          ),
        );
    }
  }

  void _nextStep() {
    if (currentStep < steps.length - 1) {
      setState(() {
        currentStep++;
      });
    } else {
      _completeTutorial();
    }
  }

  void _navigateToScreen(String screenName) {
    switch (screenName) {
      case 'inventory':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const InventoryScreen(),
          ),
        );
        break;
    }
  }

  void _completeTutorial() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('¡Tutorial Completado!'),
        content: const Text(
          '¡El mundo de Albion es tuyo!\n\nINICIA TU IMPERIO COMERCIAL YA!\n\nPuedes usar el botón "Restablecer Balance" para empezar de nuevo.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('FINALIZAR'),
          ),
        ],
      ),
    );
  }
}

class TutorialStep {
  final String title;
  final String description;
  final String instruction;
  final TutorialActionType actionType;
  final Map<String, dynamic>? data;
  final String? targetScreen;

  TutorialStep({
    required this.title,
    required this.description,
    required this.instruction,
    required this.actionType,
    this.data,
    this.targetScreen,
  });
}

enum TutorialActionType {
  introduction,
  navigation,
  addHarvest,
  addBreeding,
  directPurchase,
  createProduct,
  craftProduct,
  publishToMarket,
  sellProduct,
}
