import 'package:flutter/material.dart';
import '../models/tutorial_step.dart';
import '../services/tutorial_service.dart';
import '../services/number_format_service.dart';

class TutorialScreen extends StatefulWidget {
  const TutorialScreen({super.key});

  @override
  State<TutorialScreen> createState() => _TutorialScreenState();
}

class _TutorialScreenState extends State<TutorialScreen> {
  TutorialProgress? _progress;
  List<TutorialStep> _steps = [];
  int _currentStepIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTutorial();
  }

  Future<void> _loadTutorial() async {
    setState(() => _isLoading = true);
    
    _steps = TutorialService.getCerdoAsadoTutorialSteps();
    _progress = await TutorialService.getTutorialProgress();
    
    if (_progress == null) {
      await TutorialService.startTutorial();
      _progress = await TutorialService.getTutorialProgress();
    }

    if (_progress != null) {
      final currentStepId = _progress!.currentStepId;
      _currentStepIndex = _steps.indexWhere((step) => step.id == currentStepId);
      if (_currentStepIndex == -1) _currentStepIndex = 0;
    }

    setState(() => _isLoading = false);
  }

  Future<void> _nextStep() async {
    if (_currentStepIndex < _steps.length - 1) {
      final currentStep = _steps[_currentStepIndex];
      final nextStep = _steps[_currentStepIndex + 1];
      
      await TutorialService.completeStep(currentStep.id, nextStep.id);
      
      setState(() {
        _currentStepIndex++;
      });
      
      _progress = await TutorialService.getTutorialProgress();
    } else {
      // Tutorial completado
      await TutorialService.completeStep(_steps.last.id, 'completed');
      await TutorialService.markTutorialAsSeen();
      
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('¡Tutorial completado! Ya eres un experto en Albion Online ROI'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _executeStepAction() async {
    final currentStep = _steps[_currentStepIndex];
    
    if (currentStep.action == null) {
      _nextStep();
      return;
    }

    switch (currentStep.action!) {
      case TutorialAction.addHarvest:
        await _executeHarvestAction(currentStep.actionData!);
        break;
      case TutorialAction.addBreeding:
        await _executeBreedingAction(currentStep.actionData!);
        break;
      case TutorialAction.addDirectPurchase:
        await _executePurchaseAction(currentStep.actionData!);
        break;
      case TutorialAction.createProduct:
        await _executeCreateProductAction(currentStep.actionData!);
        break;
      default:
        _nextStep();
    }
  }

  Future<void> _executeHarvestAction(Map<String, dynamic> data) async {
    // Navegar a la pantalla de agregar cosecha con datos prellenados
    Navigator.of(context).pushNamed('/add_harvest', arguments: {
      'tutorialData': data,
      'isTutorial': true,
    });
  }

  Future<void> _executeBreedingAction(Map<String, dynamic> data) async {
    // Navegar a la pantalla de agregar cría con datos prellenados
    Navigator.of(context).pushNamed('/add_breeding', arguments: {
      'tutorialData': data,
      'isTutorial': true,
    });
  }

  Future<void> _executePurchaseAction(Map<String, dynamic> data) async {
    // Navegar a la pantalla de compra directa con datos prellenados
    Navigator.of(context).pushNamed('/add_direct_purchase', arguments: {
      'tutorialData': data,
      'isTutorial': true,
    });
  }

  Future<void> _executeCreateProductAction(Map<String, dynamic> data) async {
    // Navegar a la pantalla de definir producto con datos prellenados
    Navigator.of(context).pushNamed('/product_definition', arguments: {
      'tutorialData': data,
      'isTutorial': true,
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final currentStep = _steps[_currentStepIndex];
    final progressPercentage = (_currentStepIndex + 1) / _steps.length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tutorial: Cerdo Asado'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Barra de progreso
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Paso ${_currentStepIndex + 1} de ${_steps.length}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progressPercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          
          // Contenido del paso
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Título del paso
                  Text(
                    currentStep.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Descripción del paso
                  Expanded(
                    child: SingleChildScrollView(
                      child: Text(
                        currentStep.description,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          height: 1.5,
                        ),
                      ),
                    ),
                  ),
                  
                  // Imagen si existe
                  if (currentStep.imagePath != null) ...[
                    const SizedBox(height: 16),
                    Center(
                      child: Image.asset(
                        currentStep.imagePath!,
                        height: 200,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          // Botones de navegación
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentStepIndex > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _currentStepIndex--;
                        });
                      },
                      child: const Text('Anterior'),
                    ),
                  ),
                if (_currentStepIndex > 0) const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _executeStepAction,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(
                      currentStep.action != null 
                        ? 'Realizar Acción'
                        : (_currentStepIndex == _steps.length - 1 ? 'Finalizar' : 'Siguiente'),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
