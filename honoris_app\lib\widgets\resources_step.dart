import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/calculation_provider.dart';
import '../models/tier.dart';

class ResourcesStep extends StatelessWidget {
  const ResourcesStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CalculationProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Paso 2: Recursos y Tiers',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Define los tiers, cantidades y precios para el cálculo.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 24),

              // Configuración de Tiers
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Configuración de Tiers',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Tier de Inicio
                      Text(
                        'Tier de Inicio',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<Tier>(
                        value: provider.config.startingTier,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona el tier inicial',
                        ),
                        items: Tier.availableStartTiers.map((tier) {
                          return DropdownMenuItem(
                            value: tier,
                            child: Text(tier.displayName),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            provider.updateStartingTier(value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Tier Aspirado (Límite)
                      Text(
                        'Tier Aspirado',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<Tier>(
                        value: provider.config.craftingLimitTier,
                        decoration: const InputDecoration(
                          hintText: 'Selecciona el tier objetivo',
                        ),
                        items: _getAvailableLimitTiers(provider.config.startingTier).map((tier) {
                          return DropdownMenuItem(
                            value: tier,
                            child: Text(tier.displayName),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            provider.updateCraftingLimitTier(value);
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Cantidad Inicial
                      Text(
                        'Cantidad Inicial de Materias Primas (${provider.config.startingTier.displayName})',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        initialValue: provider.config.initialQuantity.toString(),
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: 'Cantidad inicial',
                        ),
                        onChanged: (value) {
                          final intValue = int.tryParse(value) ?? 0;
                          provider.updateInitialQuantity(intValue);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Precio de Compra del Tier Anterior (si aplica)
              if (provider.config.startingTier != Tier.t2 && provider.config.startingTier.previous != null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Precio de Compra de Material Anterior',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Precio Compra Refinado (${provider.config.startingTier.previous!.displayName})',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          initialValue: (provider.config.buyingPrices[provider.config.startingTier.previous!] ?? 0).toString(),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          decoration: const InputDecoration(
                            hintText: 'Precio de compra',
                          ),
                          onChanged: (value) {
                            final doubleValue = double.tryParse(value) ?? 0.0;
                            provider.updateBuyingPrice(provider.config.startingTier.previous!, doubleValue);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              if (provider.config.startingTier != Tier.t2) const SizedBox(height: 16),

              // Costos de Materia Prima por Tier
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Costos de Materia Prima por Tier',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      ..._buildTierInputs(
                        context,
                        provider,
                        'Materias Primas',
                        (tier, value) => provider.updateRawMaterialCost(tier, value),
                        (tier) => provider.config.rawMaterialCosts[tier] ?? 0,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Precios de Venta por Tier
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Precios de Venta Refinado por Tier',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      ..._buildTierInputs(
                        context,
                        provider,
                        'Venta',
                        (tier, value) => provider.updateSellingPrice(tier, value),
                        (tier) => provider.config.sellingPrices[tier] ?? 0,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }

  List<Tier> _getAvailableLimitTiers(Tier startingTier) {
    final startIndex = Tier.values.indexOf(startingTier);
    return Tier.values.sublist(startIndex);
  }

  List<Widget> _buildTierInputs(
    BuildContext context,
    CalculationProvider provider,
    String label,
    Function(Tier, double) onChanged,
    double Function(Tier) getValue,
  ) {
    final startIndex = Tier.values.indexOf(provider.config.startingTier);
    final limitIndex = Tier.values.indexOf(provider.config.craftingLimitTier);
    final relevantTiers = Tier.values.sublist(startIndex, limitIndex + 1);

    return relevantTiers.map((tier) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${tier.displayName} $label',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: getValue(tier).toString(),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                hintText: 'Precio para ${tier.displayName}',
              ),
              onChanged: (value) {
                final doubleValue = double.tryParse(value) ?? 0.0;
                onChanged(tier, doubleValue);
              },
            ),
          ],
        ),
      );
    }).toList();
  }
}
