import 'package:json_annotation/json_annotation.dart';

part 'tutorial_step.g.dart';

@JsonSerializable()
class TutorialStep {
  final String id;
  final String title;
  final String description;
  final String? imagePath;
  final TutorialAction? action;
  final Map<String, dynamic>? actionData;
  final bool isCompleted;

  const TutorialStep({
    required this.id,
    required this.title,
    required this.description,
    this.imagePath,
    this.action,
    this.actionData,
    this.isCompleted = false,
  });

  factory TutorialStep.fromJson(Map<String, dynamic> json) =>
      _$TutorialStepFromJson(json);

  Map<String, dynamic> toJson() => _$TutorialStepToJson(this);

  TutorialStep copyWith({
    String? id,
    String? title,
    String? description,
    String? imagePath,
    TutorialAction? action,
    Map<String, dynamic>? actionData,
    bool? isCompleted,
  }) {
    return TutorialStep(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
      action: action ?? this.action,
      actionData: actionData ?? this.actionData,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

enum TutorialAction {
  addHarvest,
  addBreeding,
  addDirectPurchase,
  createProduct,
  craftProduct,
  publishToMarket,
  sellProduct,
  viewBalance,
  viewReports,
  navigate,
}

@JsonSerializable()
class TutorialProgress {
  final String currentStepId;
  final List<String> completedSteps;
  final bool isCompleted;
  final DateTime startedAt;
  final DateTime? completedAt;

  const TutorialProgress({
    required this.currentStepId,
    required this.completedSteps,
    required this.isCompleted,
    required this.startedAt,
    this.completedAt,
  });

  factory TutorialProgress.fromJson(Map<String, dynamic> json) =>
      _$TutorialProgressFromJson(json);

  Map<String, dynamic> toJson() => _$TutorialProgressToJson(this);

  TutorialProgress copyWith({
    String? currentStepId,
    List<String>? completedSteps,
    bool? isCompleted,
    DateTime? startedAt,
    DateTime? completedAt,
  }) {
    return TutorialProgress(
      currentStepId: currentStepId ?? this.currentStepId,
      completedSteps: completedSteps ?? this.completedSteps,
      isCompleted: isCompleted ?? this.isCompleted,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}
