import 'package:json_annotation/json_annotation.dart';

part 'harvest_data.g.dart';

@JsonSerializable()
class HarvestData {
  final String cropName;
  final double seedPrice;
  final bool purchasedInMarket; // true = mercado (2.5%), false = isla (0%)
  final int seedsPurchased;
  final int seedsReceived; // Semillas recibidas después de la cosecha
  final int harvestReceived; // Cantidad de cosecha recibida
  final double focusPerSeed; // Foco usado por semilla (opcional)

  const HarvestData({
    required this.cropName,
    required this.seedPrice,
    required this.purchasedInMarket,
    required this.seedsPurchased,
    required this.seedsReceived,
    required this.harvestReceived,
    required this.focusPerSeed,
  });

  factory HarvestData.fromJson(Map<String, dynamic> json) =>
      _$HarvestDataFromJson(json);

  Map<String, dynamic> toJson() => _$HarvestDataToJson(this);

  /// Calcula el precio por unidad de cosecha
  double get pricePerUnit {
    if (harvestReceived == 0) return 0;
    
    final effectiveSeedPrice = purchasedInMarket 
        ? seedPrice * 1.025 // Agregar 2.5% de tasa
        : seedPrice;
    
    final seedsLost = seedsPurchased - seedsReceived;
    final totalSeedCost = seedsLost * effectiveSeedPrice;
    
    return totalSeedCost / harvestReceived;
  }

  /// Calcula el foco por unidad de cosecha
  double get focusPerUnit {
    if (harvestReceived == 0) return 0;
    
    final totalFocus = seedsPurchased * focusPerSeed;
    return totalFocus / harvestReceived;
  }

  /// Valida que los datos sean correctos
  List<String> validate() {
    final errors = <String>[];
    
    if (cropName.trim().isEmpty) {
      errors.add('El nombre del cultivo es requerido');
    }
    
    if (seedPrice <= 0) {
      errors.add('El precio de la semilla debe ser mayor a 0');
    }
    
    if (seedsPurchased <= 0) {
      errors.add('Las semillas compradas deben ser mayor a 0');
    }
    
    if (seedsReceived < 0) {
      errors.add('Las semillas recibidas no pueden ser negativas');
    }
    
    if (seedsReceived > seedsPurchased) {
      errors.add('Las semillas recibidas no pueden ser mayor a las compradas');
    }
    
    if (harvestReceived <= 0) {
      errors.add('La cosecha recibida debe ser mayor a 0');
    }
    
    if (focusPerSeed < 0) {
      errors.add('El foco por semilla no puede ser negativo');
    }
    
    return errors;
  }
}
