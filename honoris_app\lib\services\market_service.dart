import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/market_listing.dart';
import '../models/inventory_item.dart';

class MarketService {
  static const String _marketListingsKey = 'market_listings';
  static const String _saleTransactionsKey = 'sale_transactions';

  /// Carga todas las publicaciones del mercado
  static Future<List<MarketListing>> loadMarketListings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final listingsJson = prefs.getString(_marketListingsKey);

      if (listingsJson == null) return [];

      final List<dynamic> listingsList = json.decode(listingsJson);
      return listingsList.map((json) => MarketListing.fromJson(json)).toList();
    } catch (e) {
      print('Error loading market listings: $e');
      return [];
    }
  }

  /// Guarda todas las publicaciones del mercado
  static Future<bool> _saveMarketListings(List<MarketListing> listings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final listingsJson = json.encode(
        listings.map((l) => l.toJson()).toList(),
      );
      return await prefs.setString(_marketListingsKey, listingsJson);
    } catch (e) {
      print('Error saving market listings: $e');
      return false;
    }
  }

  /// Carga todas las transacciones de venta
  static Future<List<SaleTransaction>> loadSaleTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = prefs.getString(_saleTransactionsKey);

      if (transactionsJson == null) return [];

      final List<dynamic> transactionsList = json.decode(transactionsJson);
      return transactionsList
          .map((json) => SaleTransaction.fromJson(json))
          .toList();
    } catch (e) {
      print('Error loading sale transactions: $e');
      return [];
    }
  }

  /// Guarda todas las transacciones de venta
  static Future<bool> _saveSaleTransactions(
    List<SaleTransaction> transactions,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = json.encode(
        transactions.map((t) => t.toJson()).toList(),
      );
      return await prefs.setString(_saleTransactionsKey, transactionsJson);
    } catch (e) {
      print('Error saving sale transactions: $e');
      return false;
    }
  }

  /// Publica un item en el mercado
  static Future<bool> listItemInMarket({
    required InventoryItem item,
    required int quantity,
    required double pricePerUnit,
    required bool hasPremium,
  }) async {
    try {
      // Validar que hay suficiente cantidad
      if (quantity > item.quantity) {
        return false;
      }

      final marketTaxPercentage = hasPremium ? 4.0 : 8.0;
      const publicationTaxPercentage = 2.5;

      // Crear la publicación en el mercado
      final listing = MarketListing(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        itemId: item.id,
        itemName: item.name,
        quantityListed: quantity,
        quantitySold: 0,
        pricePerUnit: pricePerUnit,
        marketTaxPercentage: marketTaxPercentage,
        publicationTaxPercentage: publicationTaxPercentage,
        hasPremium: hasPremium,
        listedAt: DateTime.now(),
        status: MarketListingStatus.active,
      );

      // Cargar publicaciones existentes
      final currentListings = await loadMarketListings();
      currentListings.add(listing);

      // Guardar publicaciones actualizadas
      return await _saveMarketListings(currentListings);
    } catch (e) {
      print('Error listing item in market: $e');
      return false;
    }
  }

  /// Actualiza una publicación del mercado (para reportar ventas)
  static Future<bool> updateMarketListing({
    required String listingId,
    required int newQuantitySold,
  }) async {
    try {
      final currentListings = await loadMarketListings();
      final listingIndex = currentListings.indexWhere((l) => l.id == listingId);

      if (listingIndex == -1) return false;

      final listing = currentListings[listingIndex];
      final additionalSold = newQuantitySold - listing.quantitySold;

      if (additionalSold <= 0) return true; // No hay nuevas ventas

      // Crear transacción de venta
      final transaction = SaleTransaction.fromMarketSale(
        listing: listing,
        quantitySold: additionalSold,
      );

      // Actualizar la publicación
      final updatedListing = listing.copyWith(
        quantitySold: newQuantitySold,
        lastUpdatedAt: DateTime.now(),
        status: newQuantitySold >= listing.quantityListed
            ? MarketListingStatus.sold
            : MarketListingStatus.active,
      );

      currentListings[listingIndex] = updatedListing;

      // Guardar transacción
      final currentTransactions = await loadSaleTransactions();
      currentTransactions.add(transaction);

      // Guardar ambos
      final listingsSuccess = await _saveMarketListings(currentListings);
      final transactionsSuccess = await _saveSaleTransactions(
        currentTransactions,
      );

      return listingsSuccess && transactionsSuccess;
    } catch (e) {
      print('Error updating market listing: $e');
      return false;
    }
  }

  /// Cancela una publicación del mercado
  static Future<bool> cancelMarketListing(String listingId) async {
    try {
      final currentListings = await loadMarketListings();
      final listingIndex = currentListings.indexWhere((l) => l.id == listingId);

      if (listingIndex == -1) return false;

      final updatedListing = currentListings[listingIndex].copyWith(
        status: MarketListingStatus.cancelled,
        lastUpdatedAt: DateTime.now(),
      );

      currentListings[listingIndex] = updatedListing;
      return await _saveMarketListings(currentListings);
    } catch (e) {
      print('Error cancelling market listing: $e');
      return false;
    }
  }

  /// Obtiene publicaciones activas
  static Future<List<MarketListing>> getActiveListings() async {
    final allListings = await loadMarketListings();
    return allListings.where((l) => l.isActive).toList();
  }

  /// Obtiene publicaciones vendidas
  static Future<List<MarketListing>> getSoldListings() async {
    final allListings = await loadMarketListings();
    return allListings
        .where((l) => l.status == MarketListingStatus.sold)
        .toList();
  }

  /// Calcula el valor total en el mercado
  static Future<double> getTotalMarketValue() async {
    final activeListings = await getActiveListings();
    return activeListings.fold<double>(
      0.0,
      (sum, listing) => sum + listing.marketValue,
    );
  }

  /// Calcula las ganancias totales de ventas
  static Future<double> getTotalSalesProfit() async {
    final transactions = await loadSaleTransactions();
    return transactions.fold<double>(
      0.0,
      (sum, transaction) => sum + transaction.netRevenue,
    );
  }

  /// Calcula los impuestos totales pagados
  static Future<double> getTotalTaxesPaid() async {
    final transactions = await loadSaleTransactions();
    return transactions.fold<double>(
      0.0,
      (sum, transaction) =>
          sum + transaction.marketTax + transaction.publicationTax,
    );
  }

  /// Elimina una publicación del mercado
  static Future<bool> deleteMarketListing(String listingId) async {
    try {
      final currentListings = await loadMarketListings();
      currentListings.removeWhere((l) => l.id == listingId);
      return await _saveMarketListings(currentListings);
    } catch (e) {
      print('Error deleting market listing: $e');
      return false;
    }
  }
}
