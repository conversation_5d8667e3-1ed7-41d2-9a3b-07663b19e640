import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/calculation_provider.dart';
import '../widgets/step_indicator.dart';
import '../widgets/configuration_step.dart';
import '../widgets/resources_step.dart';
import '../widgets/results_step.dart';

class ConfigurationScreen extends StatefulWidget {
  final int initialStep;

  const ConfigurationScreen({super.key, this.initialStep = 0});

  @override
  State<ConfigurationScreen> createState() => _ConfigurationScreenState();
}

class _ConfigurationScreenState extends State<ConfigurationScreen> {
  late PageController _pageController;
  int _currentStep = 0;

  @override
  void initState() {
    super.initState();
    _currentStep = widget.initialStep;
    _pageController = PageController(initialPage: _currentStep);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculadora de Refinado'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          if (_currentStep == 2) // Solo mostrar en la pantalla de resultados
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveCalculation,
              tooltip: 'Guardar Cálculo',
            ),
        ],
      ),
      body: Column(
        children: [
          // Indicador de pasos
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surface,
            child: StepIndicator(
              currentStep: _currentStep,
              totalSteps: 3,
              stepTitles: const ['Configuración', 'Recursos', 'Resultados'],
            ),
          ),

          // Contenido de los pasos
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: const [
                ConfigurationStep(),
                ResourcesStep(),
                ResultsStep(),
              ],
            ),
          ),

          // Botones de navegación
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _previousStep,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Anterior'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _currentStep < 2
                        ? _nextStep
                        : _performCalculation,
                    icon: Icon(
                      _currentStep < 2 ? Icons.arrow_forward : Icons.calculate,
                    ),
                    label: Text(_currentStep < 2 ? 'Siguiente' : 'Calcular'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {
      // Validar antes de avanzar
      final provider = context.read<CalculationProvider>();
      final errors = provider.validateConfiguration();

      if (errors.isNotEmpty && _currentStep == 0) {
        _showValidationErrors(errors);
        return;
      }

      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _performCalculation() {
    final provider = context.read<CalculationProvider>();
    final errors = provider.validateForCalculation();

    if (errors.isNotEmpty) {
      _showValidationErrors(errors);
      return;
    }

    provider.performCalculation();
  }

  void _showValidationErrors(List<String> errors) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Errores de Validación'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: errors
              .map(
                (error) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(error)),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Entendido'),
          ),
        ],
      ),
    );
  }

  void _saveCalculation() async {
    final nameController = TextEditingController();
    final now = DateTime.now();
    nameController.text =
        'Cálculo ${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Guardar Cálculo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Ingresa un nombre para este cálculo:'),
            const SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Nombre del cálculo',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Guardar'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      final provider = context.read<CalculationProvider>();
      final success = await provider.saveCurrentCalculation(
        nameController.text,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Cálculo guardado exitosamente'
                  : 'Error al guardar el cálculo',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }

    nameController.dispose();
  }
}
