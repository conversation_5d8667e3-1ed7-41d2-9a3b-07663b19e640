// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crafting_product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CraftingProduct _$CraftingProductFromJson(Map<String, dynamic> json) =>
    CraftingProduct(
      id: json['id'] as String,
      name: json['name'] as String,
      objectPower: (json['objectPower'] as num).toInt(),
      craftedQuantity: (json['craftedQuantity'] as num).toInt(),
      ingredients: (json['ingredients'] as List<dynamic>)
          .map((e) => Ingredient.fromJson(e as Map<String, dynamic>))
          .toList(),
      focusPerBatch: (json['focusPerBatch'] as num).toDouble(),
      isRawMaterial: json['isRawMaterial'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CraftingProductToJson(CraftingProduct instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'objectPower': instance.objectPower,
      'craftedQuantity': instance.craftedQuantity,
      'ingredients': instance.ingredients,
      'focusPerBatch': instance.focusPerBatch,
      'isRawMaterial': instance.isRawMaterial,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
