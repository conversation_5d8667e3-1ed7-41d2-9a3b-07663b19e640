1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.honoris_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:41:13-72
19-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:42:13-50
21-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.example.honoris_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.example.honoris_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30
31    <application
32        android:name="android.app.Application"
32-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:4:9-42
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
34        android:extractNativeLibs="true"
35        android:icon="@mipmap/launcher_icon"
35-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:5:9-45
36        android:label="Albion Online Cuentas Claras" >
36-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:3:9-53
37        <activity
37-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:6:9-27:20
38            android:name="com.example.honoris_app.MainActivity"
38-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:7:13-41
39            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
39-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:12:13-163
40            android:exported="true"
40-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:8:13-36
41            android:hardwareAccelerated="true"
41-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:13:13-47
42            android:launchMode="singleTop"
42-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:9:13-43
43            android:taskAffinity=""
43-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:10:13-36
44            android:theme="@style/LaunchTheme"
44-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:11:13-47
45            android:windowSoftInputMode="adjustResize" >
45-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:14:13-55
46
47            <!--
48                 Specifies an Android theme to apply to this Activity as soon as
49                 the Android process has started. This theme is visible to the user
50                 while the Flutter UI initializes. After that, this theme continues
51                 to determine the Window background behind the Flutter UI.
52            -->
53            <meta-data
53-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:19:13-22:17
54                android:name="io.flutter.embedding.android.NormalTheme"
54-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:20:15-70
55                android:resource="@style/NormalTheme" />
55-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:21:15-52
56
57            <intent-filter>
57-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:23:13-26:29
58                <action android:name="android.intent.action.MAIN" />
58-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:24:17-68
58-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:24:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:25:17-76
60-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:25:27-74
61            </intent-filter>
62        </activity>
63        <!--
64             Don't delete the meta-data below.
65             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
66        -->
67        <meta-data
67-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:30:9-32:33
68            android:name="flutterEmbedding"
68-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:31:13-44
69            android:value="2" />
69-->E:\herd\honoris_old\honoris_app\android\app\src\main\AndroidManifest.xml:32:13-30
70
71        <activity
71-->[:url_launcher_android] E:\herd\honoris_old\honoris_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
72            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
72-->[:url_launcher_android] E:\herd\honoris_old\honoris_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
73            android:exported="false"
73-->[:url_launcher_android] E:\herd\honoris_old\honoris_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
74            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
74-->[:url_launcher_android] E:\herd\honoris_old\honoris_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
75
76        <uses-library
76-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
77            android:name="androidx.window.extensions"
77-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
78            android:required="false" />
78-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
79        <uses-library
79-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
80            android:name="androidx.window.sidecar"
80-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
81            android:required="false" />
81-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
82
83        <provider
83-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
84            android:name="androidx.startup.InitializationProvider"
84-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
85            android:authorities="com.example.honoris_app.androidx-startup"
85-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
86            android:exported="false" >
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
93        </provider>
94
95        <receiver
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
96            android:name="androidx.profileinstaller.ProfileInstallReceiver"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
97            android:directBootAware="false"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
98            android:enabled="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
99            android:exported="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
100            android:permission="android.permission.DUMP" >
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
102                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
105                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
108                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
111                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
112            </intent-filter>
113        </receiver>
114    </application>
115
116</manifest>
