import 'package:intl/intl.dart';

/// Servicio para formatear números con el sistema europeo
/// Formato: 123.456,52 (punto para miles, coma para decimales)
class NumberFormatService {
  static final NumberFormat _currencyFormat = NumberFormat.currency(
    locale: 'es_ES',
    symbol: '\$',
    decimalDigits: 2,
  );

  static final NumberFormat _numberFormat = NumberFormat('#,##0.00', 'es_ES');
  static final NumberFormat _integerFormat = NumberFormat('#,##0', 'es_ES');

  /// Formatea un número como moneda con el formato europeo
  /// Ejemplo: 1234.56 -> $1.234,56
  static String formatCurrency(double value) {
    return _currencyFormat.format(value);
  }

  /// Formatea un número decimal con el formato europeo
  /// Ejemplo: 1234.56 -> 1.234,56
  static String formatNumber(double value, {int decimals = 2}) {
    if (decimals == 0) {
      return _integerFormat.format(value);
    }
    
    final formatter = NumberFormat('#,##0.${'0' * decimals}', 'es_ES');
    return formatter.format(value);
  }

  /// Formatea un número entero con el formato europeo
  /// Ejemplo: 1234 -> 1.234
  static String formatInteger(int value) {
    return _integerFormat.format(value);
  }

  /// Formatea un porcentaje con el formato europeo
  /// Ejemplo: 0.1234 -> 12,34%
  static String formatPercentage(double value, {int decimals = 2}) {
    final percentage = value * 100;
    return '${formatNumber(percentage, decimals: decimals)}%';
  }

  /// Formatea un número con sufijo de unidades (K, M, B)
  /// Ejemplo: 1234567 -> 1,23M
  static String formatCompact(double value) {
    if (value >= 1000000000) {
      return '${formatNumber(value / 1000000000, decimals: 2)}B';
    } else if (value >= 1000000) {
      return '${formatNumber(value / 1000000, decimals: 2)}M';
    } else if (value >= 1000) {
      return '${formatNumber(value / 1000, decimals: 1)}K';
    } else {
      return formatNumber(value, decimals: 0);
    }
  }

  /// Convierte un string con formato europeo a double
  /// Ejemplo: "1.234,56" -> 1234.56
  static double? parseEuropeanNumber(String value) {
    try {
      // Remover símbolo de moneda si existe
      String cleanValue = value.replaceAll('\$', '').trim();
      
      // Reemplazar punto por nada (separador de miles)
      // y coma por punto (separador decimal)
      cleanValue = cleanValue.replaceAll('.', '').replaceAll(',', '.');
      
      return double.parse(cleanValue);
    } catch (e) {
      return null;
    }
  }

  /// Formatea ganancia con color y signo
  /// Ejemplo: 123.45 -> "+$123,45" (verde), -123.45 -> "-$123,45" (rojo)
  static String formatProfit(double value) {
    final sign = value >= 0 ? '+' : '';
    return '$sign${formatCurrency(value)}';
  }

  /// Calcula y formatea el porcentaje de ganancia
  /// Ejemplo: ganancia=100, costo=400 -> "25,00%"
  static String formatProfitPercentage(double profit, double cost) {
    if (cost == 0) return '0,00%';
    final percentage = (profit / cost) * 100;
    return formatPercentage(percentage / 100);
  }

  /// Formatea la relación ganancia/foco
  /// Ejemplo: ganancia=500, foco=200 -> "2,50"
  static String formatFocusEfficiency(double profit, double focus) {
    if (focus == 0) return '0,00';
    final efficiency = profit / focus;
    return formatNumber(efficiency);
  }
}
