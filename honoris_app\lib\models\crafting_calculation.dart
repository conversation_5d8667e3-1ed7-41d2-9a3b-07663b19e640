import 'package:json_annotation/json_annotation.dart';
import 'crafting_product.dart';

part 'crafting_calculation.g.dart';

@JsonSerializable()
class CraftingCalculation {
  final String id;
  final String productId;
  final String productName;
  final int batches; // Número de lotes a fabricar
  final double rentalCost; // Costo de alquiler por lote
  final double purchasePercentage; // Tasa de compra (2.5%)
  final double salesPercentage; // Tasa de venta (4% o 8%)
  final double taxPercentage; // Tasa de publicación (2.5%)
  final double returnPercentage; // Retorno de recursos (36.5%, 43.5%, 53.9%)
  final double sellingPricePerUnit; // Precio de venta por unidad
  final DateTime createdAt;

  const CraftingCalculation({
    required this.id,
    required this.productId,
    required this.productName,
    required this.batches,
    required this.rentalCost,
    required this.purchasePercentage,
    required this.salesPercentage,
    required this.taxPercentage,
    required this.returnPercentage,
    required this.sellingPricePerUnit,
    required this.createdAt,
  });

  factory CraftingCalculation.fromJson(Map<String, dynamic> json) =>
      _$CraftingCalculationFromJson(json);

  Map<String, dynamic> toJson() => _$CraftingCalculationToJson(this);

  CraftingCalculation copyWith({
    String? id,
    String? productId,
    String? productName,
    int? batches,
    double? rentalCost,
    double? purchasePercentage,
    double? salesPercentage,
    double? taxPercentage,
    double? returnPercentage,
    double? sellingPricePerUnit,
    DateTime? createdAt,
  }) {
    return CraftingCalculation(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      batches: batches ?? this.batches,
      rentalCost: rentalCost ?? this.rentalCost,
      purchasePercentage: purchasePercentage ?? this.purchasePercentage,
      salesPercentage: salesPercentage ?? this.salesPercentage,
      taxPercentage: taxPercentage ?? this.taxPercentage,
      returnPercentage: returnPercentage ?? this.returnPercentage,
      sellingPricePerUnit: sellingPricePerUnit ?? this.sellingPricePerUnit,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

@JsonSerializable()
class CraftingResult {
  final double totalIngredientCost; // Costo total de ingredientes
  final double totalRentalCost; // Costo total de alquiler
  final double totalManufacturingCost; // Costo total de fabricación
  final double totalRevenue; // Ingresos totales por venta
  final double netRevenue; // Ingresos netos (después de tasas)
  final double publicationCost; // Costo de publicación
  final double netProfitLoss; // Ganancia/pérdida neta
  final double profitLossPercentage; // Porcentaje de ganancia/pérdida
  final double costPerUnit; // Costo por unidad
  final double revenuePerUnit; // Ingreso por unidad
  final int totalUnitsProduced; // Total de unidades producidas
  final double actualIngredientsUsed; // Ingredientes realmente usados (con retorno)

  const CraftingResult({
    required this.totalIngredientCost,
    required this.totalRentalCost,
    required this.totalManufacturingCost,
    required this.totalRevenue,
    required this.netRevenue,
    required this.publicationCost,
    required this.netProfitLoss,
    required this.profitLossPercentage,
    required this.costPerUnit,
    required this.revenuePerUnit,
    required this.totalUnitsProduced,
    required this.actualIngredientsUsed,
  });

  factory CraftingResult.fromJson(Map<String, dynamic> json) =>
      _$CraftingResultFromJson(json);

  Map<String, dynamic> toJson() => _$CraftingResultToJson(this);

  bool get isProfitable => netProfitLoss > 0;
}
