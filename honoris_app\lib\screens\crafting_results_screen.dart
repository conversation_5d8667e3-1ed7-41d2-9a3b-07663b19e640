import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/crafting_product.dart';
import '../models/crafting_calculation.dart';
import '../providers/crafting_provider.dart';
import '../services/number_format_service.dart';

class CraftingResultsScreen extends StatelessWidget {
  final CraftingProduct product;
  final CraftingCalculation calculation;
  final CraftingResult result;

  const CraftingResultsScreen({
    super.key,
    required this.product,
    required this.calculation,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Resultados: ${product.name}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            onPressed: () => _saveToInventory(context),
            icon: const Icon(Icons.save),
            tooltip: 'Guardar en Inventario',
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Resumen general
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Resumen General',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: result.isProfitable
                              ? Colors.green
                              : Colors.red,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          result.isProfitable ? 'RENTABLE' : 'PÉRDIDA',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildSummaryItem(
                          'Ingresos Netos',
                          NumberFormatService.formatCurrency(result.netRevenue),
                          '${NumberFormatService.formatNumber(((result.netRevenue / result.totalRevenue) * 100), decimals: 2)}%',
                          Colors.green,
                        ),
                      ),
                      Expanded(
                        child: _buildSummaryItem(
                          'Costo Total',
                          NumberFormatService.formatCurrency(
                            result.totalManufacturingCost,
                          ),
                          '',
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildSummaryItem(
                          'Ganancia/Pérdida',
                          NumberFormatService.formatCurrency(
                            result.netProfitLoss,
                          ),
                          '${NumberFormatService.formatNumber(result.profitLossPercentage, decimals: 2)}%',
                          result.isProfitable ? Colors.green : Colors.red,
                        ),
                      ),
                      Expanded(
                        child: _buildSummaryItem(
                          'Unidades Producidas',
                          '${result.totalUnitsProduced}',
                          'en ${calculation.batches} lote${calculation.batches > 1 ? 's' : ''}',
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Detalles de costos
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Detalles de Costos',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildDetailRow(
                    'Costo de Ingredientes',
                    result.totalIngredientCost,
                  ),
                  _buildDetailRow('Costo de Alquiler', result.totalRentalCost),
                  _buildDetailRow(
                    'Costo de Publicación',
                    result.publicationCost,
                  ),
                  const Divider(),
                  _buildDetailRow(
                    'Costo Total de Fabricación',
                    result.totalManufacturingCost,
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Detalles de ingresos
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Detalles de Ingresos',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildDetailRow('Ingresos Brutos', result.totalRevenue),
                  _buildDetailRow(
                    'Tasas de Venta (${NumberFormatService.formatNumber((calculation.salesPercentage + calculation.taxPercentage), decimals: 2)}%)',
                    result.totalRevenue - result.netRevenue,
                  ),
                  const Divider(),
                  _buildDetailRow(
                    'Ingresos Netos',
                    result.netRevenue,
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Análisis por unidad
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Análisis por Unidad',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildUnitAnalysisItem(
                          'Precio de Venta',
                          calculation.sellingPricePerUnit,
                          Colors.green,
                        ),
                      ),
                      Expanded(
                        child: _buildUnitAnalysisItem(
                          'Costo por Unidad',
                          result.costPerUnit,
                          Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildUnitAnalysisItem(
                          'Ingreso Neto/Unidad',
                          result.revenuePerUnit,
                          Colors.blue,
                        ),
                      ),
                      Expanded(
                        child: _buildUnitAnalysisItem(
                          'Ganancia/Unidad',
                          result.revenuePerUnit - result.costPerUnit,
                          result.isProfitable ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Configuración utilizada
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Configuración Utilizada',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildConfigItem(
                          'Costo Alquiler',
                          NumberFormatService.formatCurrency(
                            calculation.rentalCost,
                          ),
                        ),
                      ),
                      Expanded(
                        child: _buildConfigItem(
                          'Retorno',
                          '${NumberFormatService.formatNumber(calculation.returnPercentage, decimals: 2)}%',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  Row(
                    children: [
                      Expanded(
                        child: _buildConfigItem(
                          'Tasa Compra',
                          '${NumberFormatService.formatNumber(calculation.purchasePercentage, decimals: 2)}%',
                        ),
                      ),
                      Expanded(
                        child: _buildConfigItem(
                          'Tasa Venta',
                          '${NumberFormatService.formatNumber(calculation.salesPercentage, decimals: 2)}%',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  _buildConfigItem(
                    'Tasa Publicación',
                    '${NumberFormatService.formatNumber(calculation.taxPercentage, decimals: 2)}%',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Botones de acción
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Volver'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _saveToInventory(context),
                  icon: const Icon(Icons.save),
                  label: const Text('Guardar'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    String subtitle,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        if (subtitle.isNotEmpty)
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: color.withOpacity(0.7)),
          ),
      ],
    );
  }

  Widget _buildDetailRow(String label, double value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            NumberFormatService.formatCurrency(value),
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitAnalysisItem(String label, double value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        Text(
          NumberFormatService.formatCurrency(value),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildConfigItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  void _saveToInventory(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Guardar en Inventario'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('¿Deseas guardar este producto fabricado en el inventario?'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Se agregará al inventario:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• ${this.result.totalUnitsProduced} unidades de ${product.name}',
                  ),
                  Text(
                    '• Costo: ${NumberFormatService.formatCurrency(this.result.costPerUnit)} por unidad',
                  ),
                  Text(
                    '• Foco: ${NumberFormatService.formatNumber(product.focusPerUnit, decimals: 2)} por unidad',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Se consumirá del inventario:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  ...product.ingredients.map((ingredient) {
                    final actualUsed =
                        ingredient.quantity *
                        calculation.batches *
                        (1 - calculation.returnPercentage / 100);
                    return Text(
                      '• ${NumberFormatService.formatNumber(actualUsed, decimals: 2)} ${ingredient.itemName}',
                    );
                  }).toList(),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Guardar'),
          ),
        ],
      ),
    );

    if (result == true) {
      final provider = context.read<CraftingProvider>();

      // Mostrar indicador de carga
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Guardando en inventario...'),
            ],
          ),
        ),
      );

      final success = await provider.saveCraftedProductToInventory();

      // Cerrar indicador de carga
      Navigator.of(context).pop();

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Producto guardado en inventario exitosamente'),
            backgroundColor: Colors.green,
          ),
        );

        // Volver a la pantalla anterior
        Navigator.of(context).pop();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(provider.error ?? 'Error al guardar en inventario'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
