import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/inventory_item.dart';
import 'add_harvest_screen.dart';
import 'add_breeding_screen.dart';
import 'add_direct_purchase_screen.dart';
import 'market_screen.dart';
import 'balance_screen.dart';
import 'products_list_screen.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Cargar datos al iniciar
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CraftingProvider>().loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inventario'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.onPrimary,
          unselectedLabelColor: Theme.of(
            context,
          ).colorScheme.onPrimary.withOpacity(0.7),
          indicatorColor: Theme.of(context).colorScheme.onPrimary,
          tabs: const [
            Tab(text: 'Inventario', icon: Icon(Icons.inventory)),
            Tab(text: 'Productos', icon: Icon(Icons.build_circle)),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'market',
                child: Row(
                  children: [
                    Icon(Icons.store, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Mercado'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'balance',
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Balance General'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'add_harvest',
                child: Row(
                  children: [
                    Icon(Icons.grass, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Agregar Cosecha'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_breeding',
                child: Row(
                  children: [
                    Icon(Icons.pets, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Agregar Cría'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_purchase',
                child: Row(
                  children: [
                    Icon(Icons.shopping_cart, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Compra Directa'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<CraftingProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error al cargar inventario',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    provider.error!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      provider.clearError();
                      provider.loadData();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reintentar'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildInventoryTab(provider),
              _buildProductsTab(provider),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddItemDialog,
        child: const Icon(Icons.add),
        tooltip: 'Agregar Item',
      ),
    );
  }

  Widget _buildInventoryTab(CraftingProvider provider) {
    final allItems = provider.inventoryItems;

    if (allItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No hay items en el inventario',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Agrega cosechas, crías o compras directas',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: allItems.length,
      itemBuilder: (context, index) {
        final item = allItems[index];
        return _buildInventoryItemCard(item, provider);
      },
    );
  }

  Widget _buildProductsTab(CraftingProvider provider) {
    return const ProductsListScreen();
  }

  Widget _buildInventoryItemCard(
    InventoryItem item,
    CraftingProvider provider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getOriginColor(item.origin),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    item.origin.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Cantidad',
                    '${item.quantity.toStringAsFixed(1)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Precio',
                    '\$${item.price.toStringAsFixed(2)}',
                  ),
                ),
                if (item.focusUsed > 0)
                  Expanded(
                    child: _buildInfoItem(
                      'Foco',
                      '${item.focusUsed.toStringAsFixed(2)}',
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Valor Total',
                    '\$${(item.quantity * item.price).toStringAsFixed(2)}',
                  ),
                ),
                IconButton(
                  onPressed: () => _deleteItem(item, provider),
                  icon: const Icon(Icons.delete),
                  color: Theme.of(context).colorScheme.error,
                  tooltip: 'Eliminar',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Color _getOriginColor(ItemOrigin origin) {
    switch (origin) {
      case ItemOrigin.harvest:
        return Colors.green;
      case ItemOrigin.breeding:
        return Colors.brown;
      case ItemOrigin.directPurchase:
        return Colors.blue;
      case ItemOrigin.crafted:
        return Colors.purple;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'market':
        _navigateToMarket();
        break;
      case 'balance':
        _navigateToBalance();
        break;
      case 'add_harvest':
        _navigateToAddHarvest();
        break;
      case 'add_breeding':
        _navigateToAddBreeding();
        break;
      case 'add_purchase':
        _navigateToAddPurchase();
        break;
    }
  }

  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Agregar al Inventario'),
        content: const Text('¿Qué tipo de item deseas agregar?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToAddHarvest();
            },
            child: const Text('Cosecha'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToAddBreeding();
            },
            child: const Text('Cría'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToAddPurchase();
            },
            child: const Text('Compra Directa'),
          ),
        ],
      ),
    );
  }

  void _navigateToMarket() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const MarketScreen()));
  }

  void _navigateToBalance() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const BalanceScreen()));
  }

  void _navigateToAddHarvest() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddHarvestScreen()));
  }

  void _navigateToAddBreeding() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddBreedingScreen()));
  }

  void _navigateToAddPurchase() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddDirectPurchaseScreen()),
    );
  }

  void _deleteItem(InventoryItem item, CraftingProvider provider) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Item'),
        content: Text('¿Estás seguro de que quieres eliminar "${item.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await provider.deleteInventoryItem(item.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Item eliminado exitosamente'
                  : 'Error al eliminar el item',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}
