import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/crafting_product.dart';

class CraftingService {
  static const String _productsKey = 'crafting_products';

  /// Obtiene todos los productos de fabricación
  static Future<List<CraftingProduct>> getProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = prefs.getString(_productsKey);

      if (productsJson == null) {
        return [];
      }

      final List<dynamic> productsList = json.decode(productsJson);
      return productsList
          .map((json) => CraftingProduct.fromJson(json))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Guarda un producto de fabricación
  static Future<bool> saveProduct(CraftingProduct product) async {
    try {
      final currentProducts = await getProducts();
      
      // Buscar si ya existe un producto con el mismo ID
      final existingIndex = currentProducts.indexWhere(
        (existing) => existing.id == product.id,
      );

      if (existingIndex != -1) {
        // Actualizar producto existente
        currentProducts[existingIndex] = product;
      } else {
        // Agregar nuevo producto
        currentProducts.add(product);
      }

      return await _saveProducts(currentProducts);
    } catch (e) {
      return false;
    }
  }

  /// Elimina un producto de fabricación
  static Future<bool> deleteProduct(String id) async {
    try {
      final currentProducts = await getProducts();
      currentProducts.removeWhere((product) => product.id == id);
      return await _saveProducts(currentProducts);
    } catch (e) {
      return false;
    }
  }

  /// Guarda la lista completa de productos
  static Future<bool> _saveProducts(List<CraftingProduct> products) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = json.encode(
        products.map((product) => product.toJson()).toList(),
      );
      return await prefs.setString(_productsKey, productsJson);
    } catch (e) {
      return false;
    }
  }

  /// Limpia todos los productos
  static Future<bool> clearProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_productsKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
