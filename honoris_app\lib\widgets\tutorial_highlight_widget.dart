import 'package:flutter/material.dart';
import '../services/tutorial_guide_service.dart';

class TutorialHighlightWidget extends StatefulWidget {
  final String targetId;
  final Widget child;
  final EdgeInsets? padding;

  const TutorialHighlightWidget({
    super.key,
    required this.targetId,
    required this.child,
    this.padding,
  });

  @override
  State<TutorialHighlightWidget> createState() => _TutorialHighlightWidgetState();
}

class _TutorialHighlightWidgetState extends State<TutorialHighlightWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  final TutorialGuideService _tutorialService = TutorialGuideService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _tutorialService.addListener(_onTutorialUpdate);
    _checkIfShouldHighlight();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tutorialService.removeListener(_onTutorialUpdate);
    super.dispose();
  }

  void _onTutorialUpdate() {
    _checkIfShouldHighlight();
  }

  void _checkIfShouldHighlight() {
    if (!_tutorialService.isActive) {
      _animationController.stop();
      return;
    }

    final currentStep = _tutorialService.getCurrentStep();
    if (currentStep != null && currentStep.targetWidget == widget.targetId) {
      _animationController.repeat(reverse: true);
    } else {
      _animationController.stop();
    }
    
    if (mounted) {
      setState(() {});
    }
  }

  bool get _shouldHighlight {
    if (!_tutorialService.isActive) return false;
    final currentStep = _tutorialService.getCurrentStep();
    return currentStep != null && currentStep.targetWidget == widget.targetId;
  }

  Color get _highlightColor {
    final currentStep = _tutorialService.getCurrentStep();
    return currentStep?.highlightColor ?? Colors.blue;
  }

  @override
  Widget build(BuildContext context) {
    if (!_shouldHighlight) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            padding: widget.padding ?? const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _highlightColor,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: _highlightColor.withOpacity(0.3),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: _highlightColor.withOpacity(0.1),
              ),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

class TutorialAwareWidget extends StatefulWidget {
  final String screenName;
  final Widget child;

  const TutorialAwareWidget({
    super.key,
    required this.screenName,
    required this.child,
  });

  @override
  State<TutorialAwareWidget> createState() => _TutorialAwareWidgetState();
}

class _TutorialAwareWidgetState extends State<TutorialAwareWidget> {
  final TutorialGuideService _tutorialService = TutorialGuideService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _tutorialService.updateScreen(widget.screenName);
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

class TutorialFloatingInstructions extends StatelessWidget {
  final TutorialGuideService _tutorialService = TutorialGuideService();

  TutorialFloatingInstructions({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _tutorialService,
      builder: (context, child) {
        if (!_tutorialService.isActive) {
          return const SizedBox.shrink();
        }

        final currentStep = _tutorialService.getCurrentStep();
        if (currentStep == null) {
          return const SizedBox.shrink();
        }

        return Positioned(
          top: 100,
          left: 16,
          right: 16,
          child: Card(
            elevation: 8,
            color: currentStep.highlightColor.withOpacity(0.95),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Paso ${currentStep.id + 1}: ${currentStep.title}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: _tutorialService.completeTutorial,
                        icon: const Icon(Icons.close, color: Colors.white, size: 20),
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    currentStep.instruction,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
