import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/breeding_data.dart';
import '../models/inventory_item.dart';
import '../services/number_format_service.dart';
import '../services/tutorial_service.dart';

class AddBreedingScreen extends StatefulWidget {
  const AddBreedingScreen({super.key});

  @override
  State<AddBreedingScreen> createState() => _AddBreedingScreenState();
}

class _AddBreedingScreenState extends State<AddBreedingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _animalNameController = TextEditingController();
  final _youngPriceController = TextEditingController();
  final _youngPurchasedController = TextEditingController();
  final _youngReceivedController = TextEditingController();
  final _focusPerYoungController = TextEditingController();
  final _foodPerDayController = TextEditingController(text: '9');
  final _timeToGrowController = TextEditingController(text: '24');

  bool _purchasedInMarket = false;
  bool _isLoading = false;
  InventoryItem? _selectedFood;
  List<InventoryItem> _availableFood = [];

  @override
  void initState() {
    super.initState();
    _loadAvailableFood();
  }

  @override
  void dispose() {
    _animalNameController.dispose();
    _youngPriceController.dispose();
    _youngPurchasedController.dispose();
    _youngReceivedController.dispose();
    _focusPerYoungController.dispose();
    _foodPerDayController.dispose();
    _timeToGrowController.dispose();
    super.dispose();
  }

  void _autoFillTutorialData() {
    TutorialService.autoFillBreedingForm(
      animalController: _animalNameController,
      priceController: _youngPriceController,
      boughtController: _youngPurchasedController,
      bornController: _youngReceivedController,
      feedPerDayController: _foodPerDayController,
      growthTimeController: _timeToGrowController,
    );

    // Set the selected food to Maíz if available
    final maizItems = _availableFood.where(
      (item) => item.name.toLowerCase().contains('maíz'),
    );

    setState(() {
      _selectedFood = maizItems.isNotEmpty ? maizItems.first : null;
    });

    // Show detailed tutorial message
    TutorialService.showTutorialMessage(
      context,
      'Paso 2: Cría de Lechones - Datos Explicados',
      '📊 EXPLICACIÓN DE LOS DATOS:\n\n'
          '🐷 Animal: Lechón (cría de cerdo)\n'
          '💰 Precio por cría: 26.010 monedas\n'
          '📦 Animales Comprados: 9 lechones\n'
          '🐽 Animales Nacidos: 8 crías (1 se perdió)\n'
          '🌽 Alimento: Maíz (9 unidades por día)\n'
          '⏰ Tiempo de Crecimiento: 24 horas\n\n'
          '💡 ¿Por qué estos números?\n'
          'Compramos 9 lechones, pero solo 8 sobrevivieron y crecieron. '
          'Se alimentan con el maíz que cosechamos anteriormente.\n\n'
          '⚠️ IMPORTANTE: Ahora debes hacer clic en "Guardar en Inventario" para continuar.',
    );
  }

  Future<void> _loadAvailableFood() async {
    final provider = context.read<CraftingProvider>();
    await provider.loadData();

    setState(() {
      _availableFood = provider.rawMaterials;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Agregar Cría'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Información del Animal',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        ElevatedButton.icon(
                          onPressed: _autoFillTutorialData,
                          icon: const Icon(Icons.auto_fix_high, size: 16),
                          label: const Text('Tutorial'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _animalNameController,
                      decoration: const InputDecoration(
                        labelText: 'Nombre del Animal',
                        hintText: 'Ej: Cerdo, Pollo, Vaca, etc.',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'El nombre del animal es requerido';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _youngPriceController,
                      decoration: const InputDecoration(
                        labelText: 'Precio de la Cría',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '\$ ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'El precio de la cría es requerido';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price <= 0) {
                          return 'Ingresa un precio válido mayor a 0';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CheckboxListTile(
                      title: const Text('Comprado en el Mercado'),
                      subtitle: const Text('Se agregará 2.5% de tasa'),
                      value: _purchasedInMarket,
                      onChanged: (value) {
                        setState(() {
                          _purchasedInMarket = value ?? false;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Datos de Cría',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _youngPurchasedController,
                            decoration: const InputDecoration(
                              labelText: 'Crías Compradas',
                              hintText: '0',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final young = int.tryParse(value);
                              if (young == null || young <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _youngReceivedController,
                            decoration: const InputDecoration(
                              labelText: 'Crías que Nacieron',
                              hintText: '0',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final received = int.tryParse(value);
                              final purchased = int.tryParse(
                                _youngPurchasedController.text,
                              );
                              if (received == null || received < 0) {
                                return 'No puede ser negativo';
                              }
                              if (purchased != null && received > purchased) {
                                return 'No puede ser mayor a las compradas';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _focusPerYoungController,
                      decoration: const InputDecoration(
                        labelText: 'Foco por Cría (Opcional)',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        helperText: 'Foco usado por cada cría',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final focus = double.tryParse(value);
                          if (focus == null || focus < 0) {
                            return 'El foco no puede ser negativo';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Alimentación',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    DropdownButtonFormField<InventoryItem>(
                      value: _selectedFood,
                      decoration: const InputDecoration(
                        labelText: 'Alimento del Inventario',
                        border: OutlineInputBorder(),
                        helperText: 'Selecciona el alimento que usaste',
                      ),
                      items: _availableFood.map((food) {
                        return DropdownMenuItem(
                          value: food,
                          child: Text(
                            '${food.name} (${NumberFormatService.formatCurrency(food.price)})',
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedFood = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Selecciona un alimento del inventario';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _foodPerDayController,
                            decoration: const InputDecoration(
                              labelText: 'Alimento por Día',
                              hintText: '9',
                              border: OutlineInputBorder(),
                              helperText: 'Cantidad diaria por animal',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final food = double.tryParse(value);
                              if (food == null || food <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _timeToGrowController,
                            decoration: const InputDecoration(
                              labelText: 'Tiempo de Crecimiento',
                              hintText: '24',
                              border: OutlineInputBorder(),
                              helperText: 'Horas para crecer',
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final time = int.tryParse(value);
                              if (time == null || time <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Mostrar cálculos en tiempo real
            _buildCalculationPreview(),

            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveBreeding,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Guardar en Inventario'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationPreview() {
    // Intentar calcular con los valores actuales
    try {
      final breedingData = _createBreedingData();
      final errors = breedingData.validate();

      if (errors.isNotEmpty) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Vista Previa del Cálculo',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Completa todos los campos para ver el cálculo',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        );
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vista Previa del Cálculo',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildPreviewItem(
                      'Precio por Animal',
                      NumberFormatService.formatCurrency(
                        breedingData.pricePerUnit,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _buildPreviewItem(
                      'Foco por Animal',
                      NumberFormatService.formatNumber(
                        breedingData.focusPerUnit,
                        decimals: 2,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              _buildPreviewItem(
                'Valor Total del Inventario',
                NumberFormatService.formatCurrency(
                  breedingData.youngPurchased * breedingData.pricePerUnit,
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  Widget _buildPreviewItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  BreedingData _createBreedingData() {
    return BreedingData(
      animalName: _animalNameController.text.trim(),
      youngPrice: double.tryParse(_youngPriceController.text) ?? 0,
      purchasedInMarket: _purchasedInMarket,
      youngPurchased: int.tryParse(_youngPurchasedController.text) ?? 0,
      youngReceived: int.tryParse(_youngReceivedController.text) ?? 0,
      focusPerYoung: double.tryParse(_focusPerYoungController.text) ?? 0,
      foodPerDay: double.tryParse(_foodPerDayController.text) ?? 0,
      foodItemId: _selectedFood?.id ?? '',
      foodItemName: _selectedFood?.name ?? '',
      foodPrice: _selectedFood?.price ?? 0,
      foodFocus: _selectedFood?.focusUsed ?? 0,
      timeToGrow: int.tryParse(_timeToGrowController.text) ?? 0,
    );
  }

  Future<void> _saveBreeding() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final breedingData = _createBreedingData();
      final errors = breedingData.validate();

      if (errors.isNotEmpty) {
        _showErrorDialog(errors.join('\n'));
        return;
      }

      final provider = context.read<CraftingProvider>();
      final success = await provider.addFromBreeding(breedingData);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cría agregada al inventario exitosamente'),
              backgroundColor: Colors.green,
            ),
          );

          // Continuar el tutorial si está activo
          TutorialService.continueAfterSave(context, 'breeding');

          Navigator.of(context).pop();
        } else {
          _showErrorDialog('Error al guardar la cría en el inventario');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error inesperado: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
