import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/forecast.dart';
import '../models/inventory_item.dart';
import '../models/market_listing.dart';
import 'market_service.dart';

/// Servicio para manejar pronósticos de venta
class ForecastService {
  static const String _forecastsKey = 'sale_forecasts';

  /// Carga todos los pronósticos guardados
  static Future<List<SaleForecast>> loadForecasts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final forecastsJson = prefs.getString(_forecastsKey);
      
      if (forecastsJson == null) return [];
      
      final List<dynamic> forecastsList = json.decode(forecastsJson);
      return forecastsList
          .map((json) => SaleForecast.fromJson(json))
          .toList();
    } catch (e) {
      print('Error loading forecasts: $e');
      return [];
    }
  }

  /// Guarda un pronóstico
  static Future<bool> saveForecast(SaleForecast forecast) async {
    try {
      final forecasts = await loadForecasts();
      
      // Remover pronóstico existente para el mismo item
      forecasts.removeWhere((f) => f.itemName == forecast.itemName);
      
      // Agregar nuevo pronóstico
      forecasts.add(forecast);
      
      return await _saveForecasts(forecasts);
    } catch (e) {
      print('Error saving forecast: $e');
      return false;
    }
  }

  /// Guarda la lista completa de pronósticos
  static Future<bool> _saveForecasts(List<SaleForecast> forecasts) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final forecastsJson = json.encode(
        forecasts.map((f) => f.toJson()).toList(),
      );
      return await prefs.setString(_forecastsKey, forecastsJson);
    } catch (e) {
      print('Error saving forecasts: $e');
      return false;
    }
  }

  /// Crea un pronóstico basado en un item del inventario
  static Future<SaleForecast> createForecast({
    required InventoryItem item,
    required double forecastPrice,
    bool hasPremium = true,
    bool isDirectSale = false,
  }) async {
    // Obtener último precio de venta si existe
    final lastSalePrice = await _getLastSalePrice(item.name);
    
    // Calcular pronóstico
    final quantity = item.quantity;
    final cost = item.price;
    final grossRevenue = quantity * forecastPrice;
    
    final marketTax = isDirectSale 
        ? 0.0 
        : grossRevenue * (hasPremium ? 0.04 : 0.08);
    final publicationTax = isDirectSale ? 0.0 : grossRevenue * 0.025;
    final netRevenue = grossRevenue - marketTax - publicationTax;
    
    final productCost = cost * quantity;
    final profit = netRevenue - productCost;
    final profitPercentage = productCost > 0 ? (profit / productCost) * 100 : 0.0;
    
    final totalFocusUsed = item.focusUsed * quantity;
    final focusEfficiency = totalFocusUsed > 0 ? profit / totalFocusUsed : 0.0;

    return SaleForecast(
      itemName: item.name,
      quantity: quantity,
      cost: cost,
      lastSalePrice: lastSalePrice,
      forecastPrice: forecastPrice,
      profit: profit,
      profitPercentage: profitPercentage,
      focusUsed: totalFocusUsed,
      focusEfficiency: focusEfficiency,
      createdAt: DateTime.now(),
    );
  }

  /// Obtiene el último precio de venta de un item
  static Future<double> _getLastSalePrice(String itemName) async {
    try {
      final transactions = await MarketService.loadSaleTransactions();
      final itemTransactions = transactions
          .where((t) => t.itemName == itemName)
          .toList();
      
      if (itemTransactions.isEmpty) return 0.0;
      
      // Ordenar por fecha más reciente
      itemTransactions.sort((a, b) => b.soldAt.compareTo(a.soldAt));
      
      return itemTransactions.first.pricePerUnit;
    } catch (e) {
      print('Error getting last sale price: $e');
      return 0.0;
    }
  }

  /// Ordena los pronósticos según el tipo especificado
  static List<SaleForecast> sortForecasts(
    List<SaleForecast> forecasts,
    SortType sortType,
  ) {
    final sortedForecasts = List<SaleForecast>.from(forecasts);
    
    switch (sortType) {
      case SortType.alphabetical:
        sortedForecasts.sort((a, b) => 
          a.itemName.toLowerCase().compareTo(b.itemName.toLowerCase()));
        break;
      case SortType.profitPercentage:
        sortedForecasts.sort((a, b) => 
          b.profitPercentage.compareTo(a.profitPercentage));
        break;
      case SortType.focusEfficiency:
        sortedForecasts.sort((a, b) => 
          b.focusEfficiency.compareTo(a.focusEfficiency));
        break;
    }
    
    return sortedForecasts;
  }

  /// Elimina un pronóstico
  static Future<bool> deleteForecast(String itemName) async {
    try {
      final forecasts = await loadForecasts();
      forecasts.removeWhere((f) => f.itemName == itemName);
      return await _saveForecasts(forecasts);
    } catch (e) {
      print('Error deleting forecast: $e');
      return false;
    }
  }

  /// Limpia todos los pronósticos
  static Future<bool> clearAllForecasts() async {
    try {
      return await _saveForecasts([]);
    } catch (e) {
      print('Error clearing forecasts: $e');
      return false;
    }
  }
}
