import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/crafting_product.dart';
import '../models/crafting_calculation.dart';
import 'crafting_results_screen.dart';

class CraftingCalculationScreen extends StatefulWidget {
  final CraftingProduct product;

  const CraftingCalculationScreen({super.key, required this.product});

  @override
  State<CraftingCalculationScreen> createState() =>
      _CraftingCalculationScreenState();
}

class _CraftingCalculationScreenState extends State<CraftingCalculationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _rentalCostController = TextEditingController(text: '300');
  final _purchasePercentageController = TextEditingController(text: '2.5');
  final _salesPercentageController = TextEditingController(text: '4');
  final _taxPercentageController = TextEditingController(text: '2.5');
  final _returnPercentageController = TextEditingController(text: '43.5');
  final _sellingPriceController = TextEditingController();
  final _batchesController = TextEditingController(text: '1');

  bool _isLoading = false;
  int _maxBatches = 0;

  @override
  void initState() {
    super.initState();
    _loadMaxBatches();
  }

  @override
  void dispose() {
    _rentalCostController.dispose();
    _purchasePercentageController.dispose();
    _salesPercentageController.dispose();
    _taxPercentageController.dispose();
    _returnPercentageController.dispose();
    _sellingPriceController.dispose();
    _batchesController.dispose();
    super.dispose();
  }

  void _loadMaxBatches() {
    final provider = context.read<CraftingProvider>();
    setState(() {
      _maxBatches = provider.getMaxBatches(widget.product.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Fabricar: ${widget.product.name}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Información del producto
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Información del Producto',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem(
                            'Producto',
                            widget.product.name,
                          ),
                        ),
                        Expanded(
                          child: _buildInfoItem(
                            'Poder',
                            '${widget.product.objectPower}',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem(
                            'Por Lote',
                            '${widget.product.craftedQuantity}',
                          ),
                        ),
                        Expanded(
                          child: _buildInfoItem(
                            'Ingredientes',
                            '${widget.product.ingredients.length}',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem(
                            'Costo/Unidad',
                            '\$${widget.product.costPerUnit.toStringAsFixed(4)}',
                          ),
                        ),
                        Expanded(
                          child: _buildInfoItem(
                            'Foco/Unidad',
                            '${widget.product.focusPerUnit.toStringAsFixed(4)}',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Configuración de fabricación
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Configuración de Fabricación',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _rentalCostController,
                            decoration: const InputDecoration(
                              labelText: 'Costo Alquiler',
                              hintText: '300',
                              border: OutlineInputBorder(),
                              helperText: 'Costo por lote',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final cost = double.tryParse(value);
                              if (cost == null || cost < 0) {
                                return 'Debe ser >= 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Botones de retorno predefinidos
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildReturnButton(
                                      '36.5%',
                                      36.5,
                                      'Sin bono/foco',
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: _buildReturnButton(
                                      '43.5%',
                                      43.5,
                                      'Con foco',
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: _buildReturnButton(
                                      '53.9%',
                                      53.9,
                                      'Bono+foco',
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // Campo de texto para retorno personalizado
                              TextFormField(
                                controller: _returnPercentageController,
                                decoration: const InputDecoration(
                                  labelText: 'Retorno (%)',
                                  hintText: '43.5',
                                  border: OutlineInputBorder(),
                                  helperText: 'O ingrese valor personalizado',
                                ),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                      decimal: true,
                                    ),
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    RegExp(r'^\d*\.?\d*'),
                                  ),
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Requerido';
                                  }
                                  final percentage = double.tryParse(value);
                                  if (percentage == null ||
                                      percentage < 0 ||
                                      percentage > 100) {
                                    return 'Entre 0 y 100';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _purchasePercentageController,
                            decoration: const InputDecoration(
                              labelText: 'Tasa Compra (%)',
                              hintText: '2.5',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final percentage = double.tryParse(value);
                              if (percentage == null || percentage < 0) {
                                return 'Debe ser >= 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _salesPercentageController,
                            decoration: const InputDecoration(
                              labelText: 'Tasa Venta (%)',
                              hintText: '4',
                              border: OutlineInputBorder(),
                              helperText: '4% premium, 8% normal',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final percentage = double.tryParse(value);
                              if (percentage == null || percentage < 0) {
                                return 'Debe ser >= 0';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _taxPercentageController,
                      decoration: const InputDecoration(
                        labelText: 'Tasa Publicación (%)',
                        hintText: '2.5',
                        border: OutlineInputBorder(),
                        helperText: 'Tasa fija de publicación',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Requerido';
                        }
                        final percentage = double.tryParse(value);
                        if (percentage == null || percentage < 0) {
                          return 'Debe ser >= 0';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Configuración de venta
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Configuración de Venta',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _sellingPriceController,
                            decoration: const InputDecoration(
                              labelText: 'Precio de Venta por Unidad',
                              hintText: '0.00',
                              border: OutlineInputBorder(),
                              prefixText: '\$ ',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*\.?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final price = double.tryParse(value);
                              if (price == null || price <= 0) {
                                return 'Debe ser > 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _batchesController,
                            decoration: InputDecoration(
                              labelText: 'Lotes a Fabricar',
                              hintText: '1',
                              border: const OutlineInputBorder(),
                              helperText: 'Máximo: $_maxBatches',
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final batches = int.tryParse(value);
                              if (batches == null || batches <= 0) {
                                return 'Debe ser > 0';
                              }
                              if (batches > _maxBatches) {
                                return 'Máximo $_maxBatches';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Vista previa de ingredientes
            _buildIngredientsPreview(),

            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _calculateCrafting,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Calcular Fabricación'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildReturnButton(String label, double value, String description) {
    final isSelected = _returnPercentageController.text == value.toString();

    return InkWell(
      onTap: () {
        setState(() {
          _returnPercentageController.text = value.toString();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
          ),
        ),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: isSelected
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              description,
              style: TextStyle(
                fontSize: 10,
                color: isSelected
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientsPreview() {
    final batches = int.tryParse(_batchesController.text) ?? 1;
    final returnPercentage =
        double.tryParse(_returnPercentageController.text) ?? 43.5;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ingredientes Necesarios (${batches} lote${batches > 1 ? 's' : ''})',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            ...widget.product.ingredients.map((ingredient) {
              final totalNeeded = ingredient.quantity * batches;
              final actualUsed = totalNeeded * (1 - returnPercentage / 100);

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        ingredient.itemName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Text(
                      '${totalNeeded.toStringAsFixed(1)} → ${actualUsed.toStringAsFixed(1)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),

            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Con ${returnPercentage.toStringAsFixed(1)}% de retorno',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _calculateCrafting() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final calculation = CraftingCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: widget.product.id,
        productName: widget.product.name,
        batches: int.parse(_batchesController.text),
        rentalCost: double.parse(_rentalCostController.text),
        purchasePercentage: double.parse(_purchasePercentageController.text),
        salesPercentage: double.parse(_salesPercentageController.text),
        taxPercentage: double.parse(_taxPercentageController.text),
        returnPercentage: double.parse(_returnPercentageController.text),
        sellingPricePerUnit: double.parse(_sellingPriceController.text),
        createdAt: DateTime.now(),
      );

      final provider = context.read<CraftingProvider>();
      provider.setCalculation(calculation);

      final success = await provider.performCalculation();

      if (mounted) {
        if (success) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => CraftingResultsScreen(
                product: widget.product,
                calculation: calculation,
                result: provider.calculationResult!,
              ),
            ),
          );
        } else {
          _showErrorDialog(provider.error ?? 'Error en el cálculo');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error inesperado: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
