// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breeding_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BreedingData _$BreedingDataFromJson(Map<String, dynamic> json) => BreedingData(
  animalName: json['animalName'] as String,
  youngPrice: (json['youngPrice'] as num).toDouble(),
  purchasedInMarket: json['purchasedInMarket'] as bool,
  youngPurchased: (json['youngPurchased'] as num).toInt(),
  youngReceived: (json['youngReceived'] as num).toInt(),
  focusPerYoung: (json['focusPerYoung'] as num).toDouble(),
  foodPerDay: (json['foodPerDay'] as num).toDouble(),
  foodItemId: json['foodItemId'] as String,
  foodItemName: json['foodItemName'] as String,
  foodPrice: (json['foodPrice'] as num).toDouble(),
  foodFocus: (json['foodFocus'] as num).toDouble(),
  timeToGrow: (json['timeToGrow'] as num).toInt(),
);

Map<String, dynamic> _$BreedingDataToJson(BreedingData instance) =>
    <String, dynamic>{
      'animalName': instance.animalName,
      'youngPrice': instance.youngPrice,
      'purchasedInMarket': instance.purchasedInMarket,
      'youngPurchased': instance.youngPurchased,
      'youngReceived': instance.youngReceived,
      'focusPerYoung': instance.focusPerYoung,
      'foodPerDay': instance.foodPerDay,
      'foodItemId': instance.foodItemId,
      'foodItemName': instance.foodItemName,
      'foodPrice': instance.foodPrice,
      'foodFocus': instance.foodFocus,
      'timeToGrow': instance.timeToGrow,
    };
