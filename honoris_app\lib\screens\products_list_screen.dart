import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/crafting_product.dart';
import 'product_definition_screen.dart';
import 'crafting_calculation_screen.dart';

class ProductsListScreen extends StatefulWidget {
  const ProductsListScreen({super.key});

  @override
  State<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends State<ProductsListScreen> {
  @override
  void initState() {
    super.initState();

    // Cargar datos al iniciar
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CraftingProvider>().loadData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Productos de Fabricación'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            onPressed: _navigateToCreateProduct,
            icon: const Icon(Icons.add),
            tooltip: 'Crear Producto',
          ),
        ],
      ),
      body: Consumer<CraftingProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error al cargar productos',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    provider.error!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      provider.clearError();
                      provider.loadData();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reintentar'),
                  ),
                ],
              ),
            );
          }

          final products = provider.craftingProducts;

          if (products.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.inventory_2, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'No hay productos definidos',
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Crea tu primer producto de fabricación',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _navigateToCreateProduct,
                    icon: const Icon(Icons.add),
                    label: const Text('Crear Producto'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return _buildProductCard(product, provider);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateProduct,
        child: const Icon(Icons.add),
        tooltip: 'Crear Producto',
      ),
    );
  }

  Widget _buildProductCard(CraftingProduct product, CraftingProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    product.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (product.isRawMaterial)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Materia Prima',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Poder', '${product.objectPower}'),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Por Lote',
                    '${product.craftedQuantity}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Ingredientes',
                    '${product.ingredients.length}',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Costo/Unidad',
                    '\$${product.costPerUnit.toStringAsFixed(4)}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Foco/Unidad',
                    '${product.focusPerUnit.toStringAsFixed(4)}',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _navigateToEditProduct(product),
                    icon: const Icon(Icons.edit),
                    label: const Text('Editar'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    
                    onPressed: product.ingredients.isEmpty
                    ? null
                  : () => _navigateToCalculation(product),
                        
                    icon: const Icon(Icons.calculate),
                    label: const Text('Fabricar'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _duplicateProduct(product),
                    icon: const Icon(Icons.copy),
                    label: const Text('Duplicar'),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _deleteProduct(product, provider),
                  icon: const Icon(Icons.delete),
                  color: Theme.of(context).colorScheme.error,
                  tooltip: 'Eliminar',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  void _navigateToCreateProduct() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ProductDefinitionScreen()),
    );
  }

  void _navigateToEditProduct(CraftingProduct product) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductDefinitionScreen(existingProduct: product),
      ),
    );
  }

  void _navigateToCalculation(CraftingProduct product) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CraftingCalculationScreen(product: product),
      ),
    );
  }

  void _duplicateProduct(CraftingProduct product) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => _DuplicateProductDialog(originalName: product.name),
    );

    if (result != null && result.isNotEmpty) {
      // Crear una copia del producto con nuevo ID y nombre
      final duplicatedProduct = product.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: result,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Navegar a la pantalla de edición con el producto duplicado
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) =>
              ProductDefinitionScreen(existingProduct: duplicatedProduct),
        ),
      );
    }
  }

  void _deleteProduct(
    CraftingProduct product,
    CraftingProvider provider,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Producto'),
        content: Text(
          '¿Estás seguro de que quieres eliminar "${product.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await provider.deleteCraftingProduct(product.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Producto eliminado exitosamente'
                  : 'Error al eliminar el producto',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}

class _DuplicateProductDialog extends StatefulWidget {
  final String originalName;

  const _DuplicateProductDialog({required this.originalName});

  @override
  State<_DuplicateProductDialog> createState() =>
      _DuplicateProductDialogState();
}

class _DuplicateProductDialogState extends State<_DuplicateProductDialog> {
  final _nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = '${widget.originalName} (Copia)';
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Duplicar Producto'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Se creará una copia de "${widget.originalName}" con todos sus ingredientes.',
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nombre del nuevo producto',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
            onFieldSubmitted: (_) => _save(),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Podrás editar los ingredientes después de crear la copia.',
                    style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(onPressed: _save, child: const Text('Duplicar')),
      ],
    );
  }

  void _save() {
    final name = _nameController.text.trim();
    if (name.isNotEmpty) {
      Navigator.of(context).pop(name);
    }
  }
}
