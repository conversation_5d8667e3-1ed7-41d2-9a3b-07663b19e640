import 'dart:math';
import '../models/inventory_item.dart';
import '../models/crafting_product.dart';
import '../models/ingredient.dart';
import '../models/market_listing.dart';
import '../services/inventory_service.dart';
import '../services/crafting_service.dart';
import '../services/market_service.dart';

class TutorialAutomationService {
  /// Ejecuta automáticamente la acción de agregar cosecha
  static Future<bool> executeAddHarvest(Map<String, dynamic> data) async {
    try {
      final itemName = data['itemName'] as String;
      final seedPrice = data['seedPrice'] as double;
      final seedsPurchased = data['seedsPurchased'] as int;
      final seedsReceived = data['seedsReceived'] as int;
      final harvestReceived = data['harvestReceived'] as int;
      final focusPerSeed = data['focusPerSeed'] as double? ?? 0.0;
      final isMarketPurchase = data['isMarketPurchase'] as bool? ?? false;

      // Calcular precio por unidad y foco por unidad
      final pricePerUnit =
          (seedsPurchased - seedsReceived) * seedPrice / harvestReceived;
      final focusPerUnit = (seedsPurchased * focusPerSeed) / harvestReceived;

      // Crear item de inventario con fecha del tutorial (hace 3 días)
      final harvestItem = InventoryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: itemName,
        quantity: harvestReceived.toDouble(),
        price: pricePerUnit,
        focusUsed: focusPerUnit,
        origin: ItemOrigin.harvest,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        isRawMaterial: true,
      );

      return await InventoryService.addInventoryItem(harvestItem);
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la acción de agregar cría
  static Future<bool> executeAddBreeding(Map<String, dynamic> data) async {
    try {
      final animalName = data['animalName'] as String;
      final breedingPrice = data['breedingPrice'] as double;
      final animalsPurchased = data['animalsPurchased'] as int;
      final animalsBorn = data['animalsBorn'] as int;
      final foodItem = data['foodItem'] as String;
      final foodPerDay = data['foodPerDay'] as int;
      final growthTime = data['growthTime'] as int;

      // Obtener el item de comida del inventario
      final inventoryItems = await InventoryService.getInventoryItems();
      final foodInventoryItem = inventoryItems.firstWhere(
        (item) => item.name == foodItem,
        orElse: () => throw Exception('Food item not found'),
      );

      // Calcular costos
      final totalFoodCost =
          foodPerDay * (growthTime / 24) * foodInventoryItem.price;
      final totalBreedingCost = animalsPurchased * breedingPrice;
      final pricePerUnit = (totalBreedingCost + totalFoodCost) / animalsBorn;

      // Crear item de inventario con fecha del tutorial (hace 2 días)
      final breedingItem = InventoryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: animalName,
        quantity: animalsBorn.toDouble(),
        price: pricePerUnit,
        focusUsed: 0.0,
        origin: ItemOrigin.breeding,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        isRawMaterial: true,
      );

      // Consumir comida del inventario
      await InventoryService.consumeInventoryItem(
        foodInventoryItem.id,
        (foodPerDay * (growthTime / 24)).toDouble(),
      );

      return await InventoryService.addInventoryItem(breedingItem);
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la acción de compra directa
  static Future<bool> executeDirectPurchase(Map<String, dynamic> data) async {
    try {
      final itemName = data['itemName'] as String;
      final originalPrice = data['originalPrice'] as double;
      final quantity = data['quantity'] as int;
      final isMarketPurchase = data['isMarketPurchase'] as bool? ?? false;

      // Calcular precio con tasa si es compra de mercado
      final finalPrice = isMarketPurchase
          ? originalPrice * 1.025
          : originalPrice;

      // Crear item de inventario con fecha del tutorial (hace 1 día)
      final purchaseItem = InventoryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: itemName,
        quantity: quantity.toDouble(),
        price: finalPrice,
        focusUsed: 0.0,
        origin: ItemOrigin.directPurchase,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        isRawMaterial: true,
      );

      return await InventoryService.addInventoryItem(purchaseItem);
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la creación de producto
  static Future<bool> executeCreateProduct(Map<String, dynamic> data) async {
    try {
      final productName = data['productName'] as String;
      final objectPower = data['objectPower'] as int;
      final craftedQuantity = data['craftedQuantity'] as int;
      final focusUsed = data['focusUsed'] as double? ?? 0.0;
      final isRawMaterial = data['isRawMaterial'] as bool? ?? false;
      final ingredientsData = data['ingredients'] as List<dynamic>;

      // Convertir ingredientes
      final ingredients = ingredientsData.map((ingredientData) {
        return Ingredient(
          itemId: ingredientData['itemId'] as String,
          itemName: ingredientData['itemName'] as String,
          quantity: ingredientData['quantity'] as double,
          pricePerUnit: ingredientData['pricePerUnit'] as double,
          focusPerUnit: ingredientData['focusPerUnit'] as double,
        );
      }).toList();

      // Crear producto
      final product = CraftingProduct(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: productName,
        objectPower: objectPower,
        craftedQuantity: craftedQuantity,
        focusPerBatch: focusUsed,
        isRawMaterial: isRawMaterial,
        ingredients: ingredients,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return await CraftingService.saveProduct(product);
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la fabricación de producto
  static Future<bool> executeCraftProduct(Map<String, dynamic> data) async {
    try {
      final productName = data['productName'] as String;
      final batches = data['batches'] as int;
      final rentalCost = data['rentalCost'] as double;
      final returnPercentage = data['returnPercentage'] as double;
      // final sellingPricePerUnit = data['sellingPricePerUnit'] as double;

      // Buscar el producto
      final products = await CraftingService.getProducts();
      final product = products.firstWhere(
        (p) => p.name == productName,
        orElse: () => throw Exception('Product not found'),
      );

      // Simular fabricación y agregar al inventario
      final totalCrafted = product.craftedQuantity * batches;
      final returnMultiplier = 1 - (returnPercentage / 100);

      // Calcular costo de ingredientes
      final inventoryItems = await InventoryService.getInventoryItems();
      double totalIngredientCost = 0.0;

      for (final ingredient in product.ingredients) {
        final inventoryItem = inventoryItems.firstWhere(
          (item) => item.name == ingredient.itemName,
          orElse: () =>
              throw Exception('Ingredient ${ingredient.itemName} not found'),
        );

        final usedQuantity = ingredient.quantity * batches * returnMultiplier;
        totalIngredientCost += usedQuantity * inventoryItem.price;

        // Consumir ingredientes del inventario
        await InventoryService.consumeInventoryItem(
          inventoryItem.id,
          usedQuantity,
        );
      }

      // Calcular costo total por unidad
      final totalCost = totalIngredientCost + rentalCost;
      final costPerUnit = totalCost / totalCrafted;

      // Crear item fabricado en el inventario
      final craftedItem = InventoryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: productName,
        quantity: totalCrafted.toDouble(),
        price: costPerUnit,
        focusUsed: product.focusPerUnit,
        origin: ItemOrigin.crafted,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isRawMaterial: product.isRawMaterial,
      );

      return await InventoryService.addInventoryItem(craftedItem);
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la publicación en el mercado
  static Future<bool> executePublishToMarket(Map<String, dynamic> data) async {
    try {
      final itemName = data['itemName'] as String;
      final quantity = data['quantity'] as int;
      final pricePerUnit = data['pricePerUnit'] as double;
      final hasPremium = data['hasPremium'] as bool? ?? true;

      // Buscar el item en el inventario
      final inventoryItems = await InventoryService.getInventoryItems();
      final item = inventoryItems.firstWhere(
        (item) => item.name == itemName,
        orElse: () => throw Exception('Item not found in inventory'),
      );

      // Publicar en el mercado
      return await MarketService.publishToMarket(
        item: item,
        quantity: quantity,
        pricePerUnit: pricePerUnit,
        hasPremium: hasPremium,
      );
    } catch (e) {
      return false;
    }
  }

  /// Ejecuta automáticamente la venta de producto
  static Future<bool> executeSellProduct(Map<String, dynamic> data) async {
    try {
      final quantitySold = data['quantitySold'] as int;

      // Obtener la última publicación activa
      final listings = await MarketService.getActiveListings();
      if (listings.isEmpty) return false;

      final listing = listings.last;

      // Simular venta con fecha de hoy
      return await MarketService.updateMarketListing(
        listingId: listing.id,
        newQuantitySold: quantitySold,
      );
    } catch (e) {
      return false;
    }
  }

  /// Genera fechas distribuidas para el tutorial
  static DateTime getTutorialDate(int daysAgo) {
    return DateTime.now().subtract(Duration(days: daysAgo));
  }
}
