import 'package:flutter/foundation.dart';
import '../models/inventory_item.dart';
import '../models/crafting_product.dart';
import '../models/crafting_calculation.dart';
import '../models/harvest_data.dart';
import '../models/breeding_data.dart';
import '../models/ingredient.dart';
import '../services/inventory_service.dart';
import '../services/crafting_calculation_service.dart';

class CraftingProvider extends ChangeNotifier {
  List<InventoryItem> _inventoryItems = [];
  List<CraftingProduct> _craftingProducts = [];
  CraftingCalculation? _currentCalculation;
  CraftingResult? _calculationResult;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<InventoryItem> get inventoryItems => _inventoryItems;
  List<CraftingProduct> get craftingProducts => _craftingProducts;
  CraftingCalculation? get currentCalculation => _currentCalculation;
  CraftingResult? get calculationResult => _calculationResult;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Getters para items específicos
  List<InventoryItem> get rawMaterials =>
      _inventoryItems.where((item) => item.isRawMaterial).toList();

  List<InventoryItem> get craftedItems =>
      _inventoryItems.where((item) => !item.isRawMaterial).toList();

  List<CraftingProduct> get availableProducts =>
      _craftingProducts.where((product) => !product.isRawMaterial).toList();

  /// Carga el inventario y productos desde el almacenamiento local
  Future<void> loadData() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _inventoryItems = await InventoryService.getInventoryItems();
      _craftingProducts = await InventoryService.getCraftingProducts();
    } catch (e) {
      _error = 'Error cargando datos: $e';
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Agrega un item al inventario desde datos de cosecha
  Future<bool> addFromHarvest(HarvestData harvestData) async {
    try {
      final item = InventoryService.createFromHarvest(harvestData);
      final success = await InventoryService.saveInventoryItem(item);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error agregando cosecha: $e';
      notifyListeners();
      return false;
    }
  }

  /// Agrega un item al inventario desde datos de cría
  Future<bool> addFromBreeding(BreedingData breedingData) async {
    try {
      final item = InventoryService.createFromBreeding(breedingData);
      final success = await InventoryService.saveInventoryItem(item);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error agregando cría: $e';
      notifyListeners();
      return false;
    }
  }

  /// Agrega un item al inventario desde compra directa
  Future<bool> addFromDirectPurchase({
    required String name,
    required double price,
    required double quantity,
    required bool purchasedInMarket,
  }) async {
    try {
      final item = InventoryService.createFromDirectPurchase(
        name: name,
        price: price,
        quantity: quantity,
        purchasedInMarket: purchasedInMarket,
      );
      final success = await InventoryService.saveInventoryItem(item);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error agregando compra directa: $e';
      notifyListeners();
      return false;
    }
  }

  /// Guarda un producto de fabricación
  Future<bool> saveCraftingProduct(CraftingProduct product) async {
    try {
      final success = await InventoryService.saveCraftingProduct(product);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error guardando producto: $e';
      notifyListeners();
      return false;
    }
  }

  /// Elimina un item del inventario
  Future<bool> deleteInventoryItem(String id) async {
    try {
      final success = await InventoryService.deleteInventoryItem(id);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error eliminando item: $e';
      notifyListeners();
      return false;
    }
  }

  /// Elimina un producto de fabricación
  Future<bool> deleteCraftingProduct(String id) async {
    try {
      final success = await InventoryService.deleteCraftingProduct(id);

      if (success) {
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error eliminando producto: $e';
      notifyListeners();
      return false;
    }
  }

  /// Obtiene items disponibles como ingredientes
  Future<List<InventoryItem>> getAvailableIngredients() async {
    return await InventoryService.getAvailableIngredients();
  }

  /// Configura un cálculo de fabricación
  void setCalculation(CraftingCalculation calculation) {
    _currentCalculation = calculation;
    _calculationResult = null;
    notifyListeners();
  }

  /// Realiza el cálculo de fabricación
  Future<bool> performCalculation() async {
    if (_currentCalculation == null) {
      _error = 'No hay cálculo configurado';
      notifyListeners();
      return false;
    }

    try {
      // Buscar el producto
      final product = _craftingProducts.firstWhere(
        (p) => p.id == _currentCalculation!.productId,
        orElse: () => throw StateError('Producto no encontrado'),
      );

      // Crear mapa de cantidades disponibles
      final availableQuantities = <String, double>{};
      for (final item in _inventoryItems) {
        availableQuantities[item.id] = item.quantity;
      }

      // Validar el cálculo
      final errors = CraftingCalculationService.validateCalculation(
        product: product,
        calculation: _currentCalculation!,
        availableQuantities: availableQuantities,
      );

      if (errors.isNotEmpty) {
        _error = errors.join('\n');
        notifyListeners();
        return false;
      }

      // Realizar el cálculo
      _calculationResult = CraftingCalculationService.calculateCrafting(
        product: product,
        calculation: _currentCalculation!,
      );

      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Error en el cálculo: $e';
      notifyListeners();
      return false;
    }
  }

  /// Calcula el máximo de lotes que se pueden fabricar
  int getMaxBatches(String productId) {
    try {
      final product = _craftingProducts.firstWhere((p) => p.id == productId);
      final availableQuantities = <String, double>{};

      for (final item in _inventoryItems) {
        availableQuantities[item.id] = item.quantity;
      }

      return CraftingCalculationService.calculateMaxBatches(
        product: product,
        availableQuantities: availableQuantities,
      );
    } catch (e) {
      return 0;
    }
  }

  /// Simula el consumo de ingredientes
  Map<String, double> simulateConsumption(String productId, int batches) {
    try {
      final product = _craftingProducts.firstWhere((p) => p.id == productId);
      final returnPercentage = _currentCalculation?.returnPercentage ?? 43.5;

      return CraftingCalculationService.simulateIngredientConsumption(
        product: product,
        batches: batches,
        returnPercentage: returnPercentage,
      );
    } catch (e) {
      return {};
    }
  }

  /// Guarda el producto fabricado en el inventario
  Future<bool> saveCraftedProductToInventory() async {
    if (_currentCalculation == null || _calculationResult == null) {
      _error = 'No hay cálculo o resultado disponible';
      notifyListeners();
      return false;
    }

    try {
      // Buscar el producto
      final product = _craftingProducts.firstWhere(
        (p) => p.id == _currentCalculation!.productId,
      );

      // Crear item de inventario desde fabricación
      final craftedItem = InventoryService.createFromCrafting(
        product: product,
        quantityProduced: _calculationResult!.totalUnitsProduced,
        costPerUnit: _calculationResult!.costPerUnit,
        focusPerUnit: product.focusPerUnit,
      );

      // Guardar en inventario
      final success = await InventoryService.saveInventoryItem(craftedItem);

      if (success) {
        // Consumir ingredientes del inventario
        await _consumeIngredients();
        await loadData(); // Recargar datos
      }

      return success;
    } catch (e) {
      _error = 'Error guardando producto fabricado: $e';
      notifyListeners();
      return false;
    }
  }

  /// Consume los ingredientes del inventario basado en el cálculo actual
  Future<void> _consumeIngredients() async {
    if (_currentCalculation == null) return;

    final product = _craftingProducts.firstWhere(
      (p) => p.id == _currentCalculation!.productId,
    );

    final consumption =
        CraftingCalculationService.simulateIngredientConsumption(
          product: product,
          batches: _currentCalculation!.batches,
          returnPercentage: _currentCalculation!.returnPercentage,
        );

    // Consumir cada ingrediente
    for (final entry in consumption.entries) {
      await InventoryService.consumeInventoryItem(entry.key, entry.value);
    }
  }

  /// Limpia el error actual
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Reinicia el estado del provider
  void reset() {
    _currentCalculation = null;
    _calculationResult = null;
    _error = null;
    notifyListeners();
  }
}
