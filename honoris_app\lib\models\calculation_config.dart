import 'package:json_annotation/json_annotation.dart';
import 'tier.dart';

part 'calculation_config.g.dart';

@JsonSerializable()
class CalculationConfig {
  final double rentalCost;
  final double purchasePercentage;
  final double salesPercentage;
  final double taxPercentage;
  final double returnPercentage;
  final Tier startingTier;
  final Tier craftingLimitTier;
  final int initialQuantity;
  final Map<Tier, double> rawMaterialCosts;
  final Map<Tier, double> sellingPrices;
  final Map<Tier, double> buyingPrices;

  const CalculationConfig({
    required this.rentalCost,
    required this.purchasePercentage,
    required this.salesPercentage,
    required this.taxPercentage,
    required this.returnPercentage,
    required this.startingTier,
    required this.craftingLimitTier,
    required this.initialQuantity,
    required this.rawMaterialCosts,
    required this.sellingPrices,
    required this.buyingPrices,
  });

  factory CalculationConfig.fromJson(Map<String, dynamic> json) =>
      _$CalculationConfigFromJson(json);

  Map<String, dynamic> toJson() => _$CalculationConfigToJson(this);

  CalculationConfig copyWith({
    double? rentalCost,
    double? purchasePercentage,
    double? salesPercentage,
    double? taxPercentage,
    double? returnPercentage,
    Tier? startingTier,
    Tier? craftingLimitTier,
    int? initialQuantity,
    Map<Tier, double>? rawMaterialCosts,
    Map<Tier, double>? sellingPrices,
    Map<Tier, double>? buyingPrices,
  }) {
    return CalculationConfig(
      rentalCost: rentalCost ?? this.rentalCost,
      purchasePercentage: purchasePercentage ?? this.purchasePercentage,
      salesPercentage: salesPercentage ?? this.salesPercentage,
      taxPercentage: taxPercentage ?? this.taxPercentage,
      returnPercentage: returnPercentage ?? this.returnPercentage,
      startingTier: startingTier ?? this.startingTier,
      craftingLimitTier: craftingLimitTier ?? this.craftingLimitTier,
      initialQuantity: initialQuantity ?? this.initialQuantity,
      rawMaterialCosts: rawMaterialCosts ?? this.rawMaterialCosts,
      sellingPrices: sellingPrices ?? this.sellingPrices,
      buyingPrices: buyingPrices ?? this.buyingPrices,
    );
  }

  static CalculationConfig get defaultConfig => CalculationConfig(
    rentalCost: 0.0,
    purchasePercentage: 2.5,
    salesPercentage: 4.0,
    taxPercentage: 2.5,
    returnPercentage: 43.5,
    startingTier: Tier.t4,
    craftingLimitTier: Tier.t4,
    initialQuantity: 500,
    rawMaterialCosts: {
      for (var tier in Tier.values) tier: 0.0,
    },
    sellingPrices: {
      for (var tier in Tier.values) tier: 0.0,
    },
    buyingPrices: {
      for (var tier in Tier.values) tier: 0.0,
    },
  );
}
