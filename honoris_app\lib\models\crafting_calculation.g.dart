// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crafting_calculation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CraftingCalculation _$CraftingCalculationFromJson(Map<String, dynamic> json) =>
    CraftingCalculation(
      id: json['id'] as String,
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      batches: (json['batches'] as num).toInt(),
      rentalCost: (json['rentalCost'] as num).toDouble(),
      purchasePercentage: (json['purchasePercentage'] as num).toDouble(),
      salesPercentage: (json['salesPercentage'] as num).toDouble(),
      taxPercentage: (json['taxPercentage'] as num).toDouble(),
      returnPercentage: (json['returnPercentage'] as num).toDouble(),
      sellingPricePerUnit: (json['sellingPricePerUnit'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$CraftingCalculationToJson(
  CraftingCalculation instance,
) => <String, dynamic>{
  'id': instance.id,
  'productId': instance.productId,
  'productName': instance.productName,
  'batches': instance.batches,
  'rentalCost': instance.rentalCost,
  'purchasePercentage': instance.purchasePercentage,
  'salesPercentage': instance.salesPercentage,
  'taxPercentage': instance.taxPercentage,
  'returnPercentage': instance.returnPercentage,
  'sellingPricePerUnit': instance.sellingPricePerUnit,
  'createdAt': instance.createdAt.toIso8601String(),
};

CraftingResult _$CraftingResultFromJson(Map<String, dynamic> json) =>
    CraftingResult(
      totalIngredientCost: (json['totalIngredientCost'] as num).toDouble(),
      totalRentalCost: (json['totalRentalCost'] as num).toDouble(),
      totalManufacturingCost: (json['totalManufacturingCost'] as num)
          .toDouble(),
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      netRevenue: (json['netRevenue'] as num).toDouble(),
      publicationCost: (json['publicationCost'] as num).toDouble(),
      netProfitLoss: (json['netProfitLoss'] as num).toDouble(),
      profitLossPercentage: (json['profitLossPercentage'] as num).toDouble(),
      costPerUnit: (json['costPerUnit'] as num).toDouble(),
      revenuePerUnit: (json['revenuePerUnit'] as num).toDouble(),
      totalUnitsProduced: (json['totalUnitsProduced'] as num).toInt(),
      actualIngredientsUsed: (json['actualIngredientsUsed'] as num).toDouble(),
    );

Map<String, dynamic> _$CraftingResultToJson(CraftingResult instance) =>
    <String, dynamic>{
      'totalIngredientCost': instance.totalIngredientCost,
      'totalRentalCost': instance.totalRentalCost,
      'totalManufacturingCost': instance.totalManufacturingCost,
      'totalRevenue': instance.totalRevenue,
      'netRevenue': instance.netRevenue,
      'publicationCost': instance.publicationCost,
      'netProfitLoss': instance.netProfitLoss,
      'profitLossPercentage': instance.profitLossPercentage,
      'costPerUnit': instance.costPerUnit,
      'revenuePerUnit': instance.revenuePerUnit,
      'totalUnitsProduced': instance.totalUnitsProduced,
      'actualIngredientsUsed': instance.actualIngredientsUsed,
    };
