// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'market_listing.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MarketListing _$MarketListingFromJson(Map<String, dynamic> json) =>
    MarketListing(
      id: json['id'] as String,
      itemId: json['itemId'] as String,
      itemName: json['itemName'] as String,
      quantityListed: (json['quantityListed'] as num).toInt(),
      quantitySold: (json['quantitySold'] as num).toInt(),
      pricePerUnit: (json['pricePerUnit'] as num).toDouble(),
      marketTaxPercentage: (json['marketTaxPercentage'] as num).toDouble(),
      publicationTaxPercentage: (json['publicationTaxPercentage'] as num)
          .toDouble(),
      hasPremium: json['hasPremium'] as bool,
      listedAt: DateTime.parse(json['listedAt'] as String),
      lastUpdatedAt: json['lastUpdatedAt'] == null
          ? null
          : DateTime.parse(json['lastUpdatedAt'] as String),
      status: $enumDecode(_$MarketListingStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$MarketListingToJson(MarketListing instance) =>
    <String, dynamic>{
      'id': instance.id,
      'itemId': instance.itemId,
      'itemName': instance.itemName,
      'quantityListed': instance.quantityListed,
      'quantitySold': instance.quantitySold,
      'pricePerUnit': instance.pricePerUnit,
      'marketTaxPercentage': instance.marketTaxPercentage,
      'publicationTaxPercentage': instance.publicationTaxPercentage,
      'hasPremium': instance.hasPremium,
      'listedAt': instance.listedAt.toIso8601String(),
      'lastUpdatedAt': instance.lastUpdatedAt?.toIso8601String(),
      'status': _$MarketListingStatusEnumMap[instance.status]!,
    };

const _$MarketListingStatusEnumMap = {
  MarketListingStatus.active: 'active',
  MarketListingStatus.sold: 'sold',
  MarketListingStatus.cancelled: 'cancelled',
  MarketListingStatus.expired: 'expired',
};

SaleTransaction _$SaleTransactionFromJson(Map<String, dynamic> json) =>
    SaleTransaction(
      id: json['id'] as String,
      marketListingId: json['marketListingId'] as String,
      itemName: json['itemName'] as String,
      quantitySold: (json['quantitySold'] as num).toInt(),
      pricePerUnit: (json['pricePerUnit'] as num).toDouble(),
      grossRevenue: (json['grossRevenue'] as num).toDouble(),
      marketTax: (json['marketTax'] as num).toDouble(),
      publicationTax: (json['publicationTax'] as num).toDouble(),
      netRevenue: (json['netRevenue'] as num).toDouble(),
      soldAt: DateTime.parse(json['soldAt'] as String),
    );

Map<String, dynamic> _$SaleTransactionToJson(SaleTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'marketListingId': instance.marketListingId,
      'itemName': instance.itemName,
      'quantitySold': instance.quantitySold,
      'pricePerUnit': instance.pricePerUnit,
      'grossRevenue': instance.grossRevenue,
      'marketTax': instance.marketTax,
      'publicationTax': instance.publicationTax,
      'netRevenue': instance.netRevenue,
      'soldAt': instance.soldAt.toIso8601String(),
    };
