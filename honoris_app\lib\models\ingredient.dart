import 'package:json_annotation/json_annotation.dart';

part 'ingredient.g.dart';

@JsonSerializable()
class Ingredient {
  final String itemId; // ID del item en el inventario
  final String itemName; // Nombre del item (para mostrar)
  final double quantity; // Cantidad necesaria
  final double pricePerUnit; // Precio por unidad (del inventario)
  final double focusPerUnit; // Foco por unidad (del inventario)

  const Ingredient({
    required this.itemId,
    required this.itemName,
    required this.quantity,
    required this.pricePerUnit,
    required this.focusPerUnit,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) =>
      _$IngredientFromJson(json);

  Map<String, dynamic> toJson() => _$IngredientToJson(this);

  Ingredient copyWith({
    String? itemId,
    String? itemName,
    double? quantity,
    double? pricePerUnit,
    double? focusPerUnit,
  }) {
    return Ingredient(
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      quantity: quantity ?? this.quantity,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      focusPerUnit: focusPerUnit ?? this.focusPerUnit,
    );
  }

  /// Costo total de este ingrediente
  double get totalCost => quantity * pricePerUnit;

  /// Foco total de este ingrediente
  double get totalFocus => quantity * focusPerUnit;
}
