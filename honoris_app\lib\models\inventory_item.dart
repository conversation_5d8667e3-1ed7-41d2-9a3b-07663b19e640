import 'package:json_annotation/json_annotation.dart';

part 'inventory_item.g.dart';

enum ItemOrigin {
  harvest('Cosecha'),
  breeding('Cría'),
  directPurchase('Compra Directa'),
  crafted('Fabricado');

  const ItemOrigin(this.displayName);
  final String displayName;
}

@JsonSerializable()
class InventoryItem {
  final String id;
  final String name;
  final double price; // Precio por unidad
  final double quantity; // Cantidad disponible
  final double focusUsed; // Foco usado por unidad
  final ItemOrigin origin;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isRawMaterial; // true para materias primas, false para productos fabricados

  const InventoryItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.focusUsed,
    required this.origin,
    required this.createdAt,
    required this.updatedAt,
    required this.isRawMaterial,
  });

  factory InventoryItem.fromJson(Map<String, dynamic> json) =>
      _$InventoryItemFromJson(json);

  Map<String, dynamic> toJson() => _$InventoryItemToJson(this);

  InventoryItem copyWith({
    String? id,
    String? name,
    double? price,
    double? quantity,
    double? focusUsed,
    ItemOrigin? origin,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isRawMaterial,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      focusUsed: focusUsed ?? this.focusUsed,
      origin: origin ?? this.origin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isRawMaterial: isRawMaterial ?? this.isRawMaterial,
    );
  }

  /// Combina este item con otro del mismo tipo usando promedio ponderado
  InventoryItem combineWith(InventoryItem other) {
    if (name != other.name) {
      throw ArgumentError('Cannot combine different items');
    }

    final totalQuantity = quantity + other.quantity;
    if (totalQuantity == 0) return this;

    // Promedio ponderado para precio
    final newPrice = (price * quantity + other.price * other.quantity) / totalQuantity;
    
    // Promedio ponderado para foco
    final newFocus = (focusUsed * quantity + other.focusUsed * other.quantity) / totalQuantity;

    return copyWith(
      price: newPrice,
      quantity: totalQuantity,
      focusUsed: newFocus,
      updatedAt: DateTime.now(),
    );
  }

  /// Reduce la cantidad del item
  InventoryItem reduceQuantity(double amount) {
    if (amount > quantity) {
      throw ArgumentError('Cannot reduce more than available quantity');
    }
    
    return copyWith(
      quantity: quantity - amount,
      updatedAt: DateTime.now(),
    );
  }

  /// Aumenta la cantidad del item
  InventoryItem increaseQuantity(double amount) {
    return copyWith(
      quantity: quantity + amount,
      updatedAt: DateTime.now(),
    );
  }
}
