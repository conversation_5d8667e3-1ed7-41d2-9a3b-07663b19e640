import 'package:flutter/foundation.dart';
import '../models/tier.dart';
import '../models/calculation_config.dart';
import '../models/calculation_result.dart';
import '../models/saved_calculation.dart';
import '../services/calculation_service.dart';
import '../services/storage_service.dart';

class CalculationProvider extends ChangeNotifier {
  CalculationConfig _config = CalculationConfig.defaultConfig;
  CalculationResult? _result;
  List<SavedCalculation> _savedCalculations = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  CalculationConfig get config => _config;
  CalculationResult? get result => _result;
  List<SavedCalculation> get savedCalculations => _savedCalculations;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Configuración básica
  void updateRentalCost(double value) {
    _config = _config.copyWith(rentalCost: value);
    notifyListeners();
  }

  void updatePurchasePercentage(double value) {
    _config = _config.copyWith(purchasePercentage: value);
    notifyListeners();
  }

  void updateSalesPercentage(double value) {
    _config = _config.copyWith(salesPercentage: value);
    notifyListeners();
  }

  void updateTaxPercentage(double value) {
    _config = _config.copyWith(taxPercentage: value);
    notifyListeners();
  }

  void updateReturnPercentage(double value) {
    _config = _config.copyWith(returnPercentage: value);
    notifyListeners();
  }

  void updateStartingTier(Tier tier) {
    _config = _config.copyWith(startingTier: tier);
    // Si el tier inicial es mayor que el límite, ajustar el límite
    if (Tier.values.indexOf(tier) >
        Tier.values.indexOf(_config.craftingLimitTier)) {
      _config = _config.copyWith(craftingLimitTier: tier);
    }
    notifyListeners();
  }

  void updateCraftingLimitTier(Tier tier) {
    _config = _config.copyWith(craftingLimitTier: tier);
    notifyListeners();
  }

  void updateInitialQuantity(int quantity) {
    _config = _config.copyWith(initialQuantity: quantity);
    notifyListeners();
  }

  void updateRawMaterialCost(Tier tier, double cost) {
    final updatedCosts = Map<Tier, double>.from(_config.rawMaterialCosts);
    updatedCosts[tier] = cost;
    _config = _config.copyWith(rawMaterialCosts: updatedCosts);
    notifyListeners();
  }

  void updateSellingPrice(Tier tier, double price) {
    final updatedPrices = Map<Tier, double>.from(_config.sellingPrices);
    updatedPrices[tier] = price;
    _config = _config.copyWith(sellingPrices: updatedPrices);
    notifyListeners();
  }

  void updateBuyingPrice(Tier tier, double price) {
    final updatedPrices = Map<Tier, double>.from(_config.buyingPrices);
    updatedPrices[tier] = price;
    _config = _config.copyWith(buyingPrices: updatedPrices);
    notifyListeners();
  }

  // Cálculos
  void performCalculation() {
    try {
      _error = null;
      _result = CalculationService.performCalculations(_config);
      notifyListeners();
    } catch (e) {
      _error = 'Error en el cálculo: $e';
      notifyListeners();
    }
  }

  // Gestión de calculaciones guardadas
  Future<void> loadSavedCalculations() async {
    _isLoading = true;
    notifyListeners();

    try {
      _savedCalculations = await StorageService.getSavedCalculations();
      _error = null;
    } catch (e) {
      _error = 'Error cargando calculaciones: $e';
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> saveCurrentCalculation(String name) async {
    try {
      final calculation = SavedCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        createdAt: DateTime.now(),
        config: _config,
      );

      final success = await StorageService.saveCalculation(calculation);
      if (success) {
        await loadSavedCalculations(); // Recargar la lista
      }
      return success;
    } catch (e) {
      _error = 'Error guardando cálculo: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> deleteCalculation(String id) async {
    try {
      final success = await StorageService.deleteCalculation(id);
      if (success) {
        await loadSavedCalculations(); // Recargar la lista
      }
      return success;
    } catch (e) {
      _error = 'Error eliminando cálculo: $e';
      notifyListeners();
      return false;
    }
  }

  void loadCalculation(SavedCalculation calculation) {
    _config = calculation.config;
    _result = null; // Limpiar resultados anteriores
    notifyListeners();
  }

  void resetConfiguration() {
    _config = CalculationConfig.defaultConfig;
    _result = null;
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Validaciones
  List<String> validateConfiguration() {
    final errors = <String>[];

    if (_config.initialQuantity <= 0) {
      errors.add('La cantidad inicial debe ser mayor a 0');
    }

    if (_config.rentalCost < 0) {
      errors.add('El costo de alquiler no puede ser negativo');
    }

    if (_config.purchasePercentage < 0) {
      errors.add('La tasa de compra no puede ser negativa');
    }

    if (_config.salesPercentage < 0) {
      errors.add('La tasa de venta no puede ser negativa');
    }

    if (_config.taxPercentage < 0) {
      errors.add('Los impuestos no pueden ser negativos');
    }

    // Validar que el tier límite sea mayor o igual al inicial
    final startIndex = Tier.values.indexOf(_config.startingTier);
    final limitIndex = Tier.values.indexOf(_config.craftingLimitTier);
    if (startIndex > limitIndex) {
      errors.add('El tier límite debe ser mayor o igual al tier inicial');
    }

    // Validar costos de materias primas para los tiers relevantes
    final relevantTiers = Tier.values.sublist(startIndex, limitIndex + 1);
    for (final tier in relevantTiers) {
      final cost = _config.rawMaterialCosts[tier] ?? 0;
      if (cost < 0) {
        errors.add(
          'El costo de materia prima para ${tier.displayName} no puede ser negativo',
        );
      }
    }

    // Validar precio de compra del tier anterior al inicial (si aplica)
    if (_config.startingTier != Tier.t2) {
      final prevTier = _config.startingTier.previous;
      if (prevTier != null) {
        final buyingPrice = _config.buyingPrices[prevTier] ?? 0;
        if (buyingPrice < 0) {
          errors.add(
            'El precio de compra para ${prevTier.displayName} no puede ser negativo',
          );
        }
      }
    }

    return errors;
  }

  // Validaciones específicas para el paso de cálculo
  List<String> validateForCalculation() {
    final errors = validateConfiguration();

    // Validar precios de venta para el tier límite solo cuando se va a calcular
    final limitTierPrice =
        _config.sellingPrices[_config.craftingLimitTier] ?? 0;
    if (limitTierPrice <= 0) {
      errors.add('El precio de venta para el tier límite debe ser mayor a 0');
    }

    return errors;
  }

  bool get isConfigurationValid => validateConfiguration().isEmpty;
}
