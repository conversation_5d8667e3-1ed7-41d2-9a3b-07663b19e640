// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calculation_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TierResult _$TierResultFromJson(Map<String, dynamic> json) => TierResult(
  quantity: (json['quantity'] as num).toInt(),
  requiredRawHides: (json['requiredRawHides'] as num).toInt(),
  requiredLeather: (json['requiredLeather'] as num).toInt(),
  costPerUnit: (json['costPerUnit'] as num).toDouble(),
  rentalCostPerUnit: (json['rentalCostPerUnit'] as num).toDouble(),
  sellingPrice: (json['sellingPrice'] as num).toDouble(),
  netSellingPrice: (json['netSellingPrice'] as num).toDouble(),
  buyingPricePrevTierMaterial: (json['buyingPricePrevTierMaterial'] as num)
      .toDouble(),
  profitLossAmount: (json['profitLossAmount'] as num).toDouble(),
  profitLossPercentage: (json['profitLossPercentage'] as num).toDouble(),
  status: $enumDecode(_$TierStatusEnumMap, json['status']),
);

Map<String, dynamic> _$TierResultToJson(TierResult instance) =>
    <String, dynamic>{
      'quantity': instance.quantity,
      'requiredRawHides': instance.requiredRawHides,
      'requiredLeather': instance.requiredLeather,
      'costPerUnit': instance.costPerUnit,
      'rentalCostPerUnit': instance.rentalCostPerUnit,
      'sellingPrice': instance.sellingPrice,
      'netSellingPrice': instance.netSellingPrice,
      'buyingPricePrevTierMaterial': instance.buyingPricePrevTierMaterial,
      'profitLossAmount': instance.profitLossAmount,
      'profitLossPercentage': instance.profitLossPercentage,
      'status': _$TierStatusEnumMap[instance.status]!,
    };

const _$TierStatusEnumMap = {
  TierStatus.profit: 'profit',
  TierStatus.loss: 'loss',
  TierStatus.notCrafted: 'notCrafted',
};

CalculationSummary _$CalculationSummaryFromJson(
  Map<String, dynamic> json,
) => CalculationSummary(
  totalRawHideCost: (json['totalRawHideCost'] as num).toDouble(),
  totalAcquiredLeatherCost: (json['totalAcquiredLeatherCost'] as num)
      .toDouble(),
  totalMaterialInvestment: (json['totalMaterialInvestment'] as num).toDouble(),
  totalRentalCost: (json['totalRentalCost'] as num).toDouble(),
  totalRevenue: (json['totalRevenue'] as num).toDouble(),
  netProfitLoss: (json['netProfitLoss'] as num).toDouble(),
  netProfitLossPercentage: (json['netProfitLossPercentage'] as num).toDouble(),
);

Map<String, dynamic> _$CalculationSummaryToJson(CalculationSummary instance) =>
    <String, dynamic>{
      'totalRawHideCost': instance.totalRawHideCost,
      'totalAcquiredLeatherCost': instance.totalAcquiredLeatherCost,
      'totalMaterialInvestment': instance.totalMaterialInvestment,
      'totalRentalCost': instance.totalRentalCost,
      'totalRevenue': instance.totalRevenue,
      'netProfitLoss': instance.netProfitLoss,
      'netProfitLossPercentage': instance.netProfitLossPercentage,
    };

CalculationResult _$CalculationResultFromJson(Map<String, dynamic> json) =>
    CalculationResult(
      summary: CalculationSummary.fromJson(
        json['summary'] as Map<String, dynamic>,
      ),
      tierResults: (json['tierResults'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
          $enumDecode(_$TierEnumMap, k),
          TierResult.fromJson(e as Map<String, dynamic>),
        ),
      ),
    );

Map<String, dynamic> _$CalculationResultToJson(CalculationResult instance) =>
    <String, dynamic>{
      'summary': instance.summary,
      'tierResults': instance.tierResults.map(
        (k, e) => MapEntry(_$TierEnumMap[k]!, e),
      ),
    };

const _$TierEnumMap = {
  Tier.t2: 't2',
  Tier.t3: 't3',
  Tier.t4: 't4',
  Tier.t5: 't5',
  Tier.t6: 't6',
  Tier.t7: 't7',
  Tier.t8: 't8',
};
