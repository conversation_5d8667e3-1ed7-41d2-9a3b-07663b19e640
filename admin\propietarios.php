<?php

require_once __DIR__ . '/../includes/users_logic.php';
// Obtener la lista de usuarios de tipo 'propietario'
$propietarios = getUsersByType($conn, 'propietario');

// Incluir el nuevo menú unificado.
$base_url = '/'; // Asegúrate que esta URL base sea correcta
?>
<!DOCTYPE html>
<html lang="es">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Honoris - Propietarios (Admin)</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
   
</head>
<body class="flex flex-col min-h-screen bg-gray-100">

    <?php include __DIR__ . '/../includes/menu.php'; // Incluir el nuevo menú unificado ?>

    <main class="container flex-grow p-4 mx-auto mt-8 sm:p-6">
        <section>
            <h2 class="mb-4 text-2xl font-semibold">Gestión de Propietarios (Admin)</h2>

             <div class="messages">
                 <?php
                 // Mostrar mensajes si hay alguno del procesamiento POST (ej: eliminación de propietario)
                 // Aunque la eliminación se hace en clients.php, si un propietario.php
                 // tuviera su propio formulario de eliminación que postea a sí mismo,
                 // la lógica en users_logic.php lo manejaría y los mensajes aparecerían aquí.
                 foreach ($user_messages as $msg) {
                     echo $msg;
                 }
                 ?>
             </div>

             <?php
             $propietario_a_editar = null;
             if (isset($_GET['id']) && is_numeric($_GET['id'])) {
                  $propietario_a_editar = getUserById($conn, (int)$_GET['id']); // Asume que getUserById existe y funciona
             }
             if ($propietario_a_editar):
             ?>
              <div id="edit-form-section" class="p-6 mb-8 bg-white rounded-lg shadow-md scroll-mt-20">
                  <h3 class="mb-4 text-xl font-semibold">Editar Propietario: <?php echo htmlspecialchars($propietario_a_editar['nombre']); ?></h3>
                  <form method="post" action="propietarios.php"> <!-- El action apunta a sí mismo para procesar el POST -->
                      <input type="hidden" name="id" value="<?php echo htmlspecialchars($propietario_a_editar['id']); ?>">
                      <!-- Asumimos que users_logic.php maneja 'update_user' o una acción similar -->
                      <input type="hidden" name="action" value="update_user"> 
                      <input type="hidden" name="tipo" value="propietario"> <!-- Importante para users_logic.php -->
                      
                      <div class="mb-4">
                          <label for="nombre_edit_<?php echo $propietario_a_editar['id']; ?>" class="block text-sm font-medium text-gray-700">Nombre:</label>
                          <input type="text" name="nombre" id="nombre_edit_<?php echo $propietario_a_editar['id']; ?>" value="<?php echo htmlspecialchars($propietario_a_editar['nombre']); ?>" class="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" required>
                      </div>
                      <!-- Aquí deberías añadir más campos del formulario de edición (email, teléfono, etc.) -->
                      <!-- Ejemplo:
                      <div class="mb-4">
                          <label for="email_edit_<?php echo $propietario_a_editar['id']; ?>" class="block text-sm font-medium text-gray-700">Email:</label>
                          <input type="email" name="email" id="email_edit_<?php echo $propietario_a_editar['id']; ?>" value="<?php echo htmlspecialchars($propietario_a_editar['email'] ?? ''); ?>" class="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      -->
                      <p class="p-3 my-3 text-sm text-yellow-700 bg-yellow-100 border-l-4 border-yellow-500">
                          <strong>Nota:</strong> Este es un formulario de edición simplificado. 
                          Debe expandirse con todos los campos necesarios para un propietario.
                          Asegúrese de que `users_logic.php` maneje la acción de actualización.
                      </p>
                      <div class="flex items-center space-x-4">
                        <button type="submit" class="px-4 py-2 font-bold text-white transition duration-150 ease-in-out bg-indigo-600 rounded-md shadow-sm hover:bg-indigo-700">Guardar Cambios</button>
                        <a href="propietarios.php" class="text-gray-600 hover:text-gray-800">Cancelar</a>
                      </div>
                  </form>
              </div>
             <?php endif; ?>
             

            <h3 class="mb-6 text-xl font-semibold text-gray-700">Lista de Propietarios</h3>
            <?php if (!empty($propietarios)): ?>
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <?php foreach ($propietarios as $row): ?>
                        <?php
                        // Obtener propiedades asociadas para este propietario
                        $propiedades_asociadas = getPropertiesByOwnerId($conn, $row['id']);
                        ?>
                        <div class="flex flex-col justify-between p-6 transition-shadow duration-300 bg-white shadow-lg rounded-xl hover:shadow-xl">
                            <div> <!-- Contenido de la tarjeta -->
                                <!-- ID y Nombre del Propietario -->
                                <div class="mb-4">
                                    <h4 class="text-2xl font-bold text-gray-800"><?php echo htmlspecialchars($row['nombre']); ?></h4>
                                    <p class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($row['id']); ?></p>
                                </div>

                                <!-- Propiedades del Propietario -->
                                <div class="mb-5">
                                    <h5 class="mb-2 font-semibold text-gray-700">Propiedades:</h5>
                                    <?php if (!empty($propiedades_asociadas)): ?>
                                        <ul class="pr-2 space-y-1 overflow-y-auto text-sm text-gray-600 list-disc list-inside max-h-48 custom-scrollbar">
                                            <?php foreach ($propiedades_asociadas as $prop): ?>
                                                <li>
                                                    <span class="font-medium"><?php echo htmlspecialchars($prop['edificio']); ?></span> en <?php echo htmlspecialchars($prop['ciudad']); ?>
                                                    <?php
                                                    // Obtener solicitudes para esta propiedad
                                                    $solicitudes_para_prop = getSolicitudesByEdificioCiudad($conn, $prop['edificio'], $prop['ciudad']);
                                                    if (!empty($solicitudes_para_prop)):
                                                        $clientes_interesados = array_column($solicitudes_para_prop, 'cliente');
                                                        echo " <span class='text-xs font-light text-gray-500'>(Solicitado por: " . htmlspecialchars(implode(", ", $clientes_interesados)) . ")</span>";
                                                    endif;
                                                    ?>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php else: ?>
                                        <p class="text-sm italic text-gray-500">No posee propiedades asignadas.</p>
                                    <?php endif; ?>
                                </div>
                            </div> <!-- Fin del contenido de la tarjeta -->

                            <!-- Botones de Acción -->
                            <div class="pt-4 mt-auto border-t border-gray-200">
                                <div class="flex flex-col space-y-2">
                                    <a href="clientes.php?id=<?php echo $row['id']; ?>" class="w-full px-4 py-2 font-semibold text-center text-white transition duration-150 ease-in-out bg-green-500 rounded-md shadow-sm hover:bg-green-600">Editar Usuario</a>
                                    
                                    <?php 
                                    $show_edit_specific_button = true;
                                    // Ocultar botón "Editar Propietario Específico" si el formulario de edición para ESTE propietario ya está visible arriba
                                    if ($propietario_a_editar && $propietario_a_editar['id'] == $row['id']) {
                                        $show_edit_specific_button = false;
                                    }
                                    if ($show_edit_specific_button): 
                                    ?>
                                        <a href="propietarios.php?id=<?php echo $row['id']; ?>#edit-form-section" class="w-full px-4 py-2 font-semibold text-center text-white transition duration-150 ease-in-out bg-blue-500 rounded-md shadow-sm hover:bg-blue-600">Editar Propietario Específico</a>
                                    <?php endif; ?>
                                    
                                    <form method="post" action="propietarios.php" onsubmit="return confirm('¿Estás seguro de que deseas eliminar este propietario y sus propiedades asociadas?');" class="w-full">
                                        <input type="hidden" name="eliminar_id" value="<?php echo $row['id']; ?>">
                                        <button type="submit" class="w-full px-4 py-2 font-semibold text-white transition duration-150 ease-in-out bg-red-500 rounded-md shadow-sm hover:bg-red-600">Eliminar Propietario</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="p-6 bg-white rounded-lg shadow">
                    <p class="text-gray-600">No hay propietarios para mostrar.</p>
                </div>
            <?php endif; ?>

        </section>
    </main>

    <?php include '../includes/footer.php'; ?>

     <?php
     // Incluir el script tabla_dinamica.js si se aplica a esta tabla
     // <script src="../public/js/tabla_dinamica.js"></script>
     ?>
    <style>
        /* Para navegadores Webkit como Chrome, Safari */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px; /* Ancho de la barra de scroll */
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background-color: #cbd5e1; /* Color del pulgar (gris claro de Tailwind) */
            border-radius: 10px; /* Bordes redondeados del pulgar */
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background-color: #f1f5f9; /* Color de la pista (gris muy claro de Tailwind) */
        }
    </style>
</body>
</html>