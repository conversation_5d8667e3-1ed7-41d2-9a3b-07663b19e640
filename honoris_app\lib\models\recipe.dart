import 'tier.dart';

class Recipe {
  final Tier tier;
  final Tier? previousTier;
  final int requiredPrevious;
  final int requiredRaw;

  const Recipe({
    required this.tier,
    this.previousTier,
    required this.requiredPrevious,
    required this.requiredRaw,
  });

  static const Map<Tier, Recipe> recipes = {
    Tier.t2: Recipe(
      tier: Tier.t2,
      previousTier: null,
      requiredPrevious: 0,
      requiredRaw: 1,
    ),
    Tier.t3: Recipe(
      tier: Tier.t3,
      previousTier: Tier.t2,
      requiredPrevious: 1,
      requiredRaw: 2,
    ),
    Tier.t4: Recipe(
      tier: Tier.t4,
      previousTier: Tier.t3,
      requiredPrevious: 1,
      requiredRaw: 2,
    ),
    Tier.t5: Recipe(
      tier: Tier.t5,
      previousTier: Tier.t4,
      requiredPrevious: 1,
      requiredRaw: 3,
    ),
    Tier.t6: Recipe(
      tier: Tier.t6,
      previousTier: Tier.t5,
      requiredPrevious: 1,
      requiredRaw: 4,
    ),
    Tier.t7: Recipe(
      tier: Tier.t7,
      previousTier: Tier.t6,
      requiredPrevious: 1,
      requiredRaw: 5,
    ),
    Tier.t8: Recipe(
      tier: Tier.t8,
      previousTier: Tier.t7,
      requiredPrevious: 1,
      requiredRaw: 5,
    ),
  };

  static Recipe? getRecipe(Tier tier) => recipes[tier];
}

class BuildingValues {
  static const Map<Tier, int> values = {
    Tier.t2: 0,
    Tier.t3: 8,
    Tier.t4: 16,
    Tier.t5: 32,
    Tier.t6: 64,
    Tier.t7: 128,
    Tier.t8: 256,
  };

  static int getValue(Tier tier) => values[tier] ?? 0;
}
