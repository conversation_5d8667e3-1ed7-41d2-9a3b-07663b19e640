import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/tutorial_step.dart';

class TutorialService {
  static const String _progressKey = 'tutorial_progress';
  static const String _hasSeenTutorialKey = 'has_seen_tutorial';

  /// Obtiene el progreso actual del tutorial
  static Future<TutorialProgress?> getTutorialProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_progressKey);

      if (progressJson == null) return null;

      final progressMap = json.decode(progressJson);
      return TutorialProgress.fromJson(progressMap);
    } catch (e) {
      return null;
    }
  }

  /// Guarda el progreso del tutorial
  static Future<bool> saveTutorialProgress(TutorialProgress progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = json.encode(progress.toJson());
      return await prefs.setString(_progressKey, progressJson);
    } catch (e) {
      return false;
    }
  }

  /// Inicia un nuevo tutorial
  static Future<bool> startTutorial() async {
    final progress = TutorialProgress(
      currentStepId: 'welcome',
      completedSteps: [],
      isCompleted: false,
      startedAt: DateTime.now(),
    );
    return await saveTutorialProgress(progress);
  }

  /// Marca un paso como completado y avanza al siguiente
  static Future<bool> completeStep(String stepId, String nextStepId) async {
    try {
      final currentProgress = await getTutorialProgress();
      if (currentProgress == null) return false;

      final updatedCompletedSteps = List<String>.from(
        currentProgress.completedSteps,
      );
      if (!updatedCompletedSteps.contains(stepId)) {
        updatedCompletedSteps.add(stepId);
      }

      final isLastStep = nextStepId == 'completed';
      final updatedProgress = currentProgress.copyWith(
        currentStepId: isLastStep ? 'completed' : nextStepId,
        completedSteps: updatedCompletedSteps,
        isCompleted: isLastStep,
        completedAt: isLastStep ? DateTime.now() : null,
      );

      return await saveTutorialProgress(updatedProgress);
    } catch (e) {
      return false;
    }
  }

  /// Verifica si el usuario ya vio el tutorial
  static Future<bool> hasSeenTutorial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_hasSeenTutorialKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Marca que el usuario ya vio el tutorial
  static Future<bool> markTutorialAsSeen() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_hasSeenTutorialKey, true);
    } catch (e) {
      return false;
    }
  }

  /// Reinicia el tutorial
  static Future<bool> resetTutorial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_progressKey);
      await prefs.remove(_hasSeenTutorialKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Obtiene todos los pasos del tutorial del cerdo asado
  static List<TutorialStep> getCerdoAsadoTutorialSteps() {
    return [
      // Paso 1: Bienvenida
      TutorialStep(
        id: 'welcome',
        title: '¡Bienvenido Empresario!',
        description:
            'Hoy inicia tu día como un empresario exitoso en Albion Online. He traído todos los recursos por ti, ahora vamos a configurar Albion Online ROI para crear el delicioso Cerdo Asado.',
      ),

      // Paso 2: Configurar cosecha de maíz
      TutorialStep(
        id: 'add_corn_harvest',
        title: 'Configurar Cosecha de Maíz',
        description:
            'Tienes 2 parcelas de maíz en tu isla. Cada semilla de maíz cuesta 26.010 y se compró en la isla (0% de tasa). Se compraron 18 semillas, se recibieron 15 con 152 maíz.',
        action: TutorialAction.addHarvest,
        actionData: {
          'itemName': 'Maíz',
          'seedPrice': 26010.0,
          'seedsPurchased': 18,
          'seedsReceived': 15,
          'harvestReceived': 152,
          'focusPerSeed': 0.0,
          'isMarketPurchase': false,
        },
      ),

      // Paso 3: Configurar cría de lechón
      TutorialStep(
        id: 'add_pig_breeding',
        title: 'Configurar Cría de Lechón',
        description:
            'Tienes 1 parcela de cerdos. Las crías de cerdos (lechón) se compraron ayer en la isla por 26.010 y se alimentan con el maíz. De los 9 cerdos solo nacieron 8 crías.',
        action: TutorialAction.addBreeding,
        actionData: {
          'animalName': 'Lechón',
          'breedingPrice': 26010.0,
          'animalsPurchased': 9,
          'animalsBorn': 8,
          'foodItem': 'Maíz',
          'foodPerDay': 9,
          'growthTime': 24,
          'isMarketPurchase': false,
        },
      ),

      // Paso 4: Comprar leche de vaca
      TutorialStep(
        id: 'add_milk_purchase',
        title: 'Comprar Leche de Vaca',
        description:
            'Nos falta la leche que se comprará en el mercado. La leche de vaca se compró en el mercado pagando 2.5% de tasa, se compraron 360 unidades por un precio de 275.',
        action: TutorialAction.addDirectPurchase,
        actionData: {
          'itemName': 'Leche de Vaca',
          'originalPrice': 275.0,
          'quantity': 360,
          'isMarketPurchase': true,
        },
      ),

      // Paso 5: Crear producto Carne de Cerdo
      TutorialStep(
        id: 'create_pork_product',
        title: 'Definir Carne de Cerdo',
        description:
            'Vamos a la carnicería a picar los cerdos. Definir Carne de cerdo, valor del objeto 40, cantidad crafteada 18, es materia prima, y usa 1 lechón por cada lote.',
        action: TutorialAction.createProduct,
        actionData: {
          'productName': 'Carne de Cerdo',
          'objectPower': 40,
          'craftedQuantity': 18,
          'isRawMaterial': true,
          'ingredients': [
            {'itemName': 'Lechón', 'quantity': 1.0},
          ],
        },
      ),

      // Paso 6: Fabricar Carne de Cerdo
      TutorialStep(
        id: 'craft_pork',
        title: 'Fabricar Carne de Cerdo',
        description:
            'Ahora vamos a fabricar la carne de cerdo. Configurar: 1 lote, alquiler 750, porcentaje de retorno 43.5%, precio de venta 275.',
        action: TutorialAction.craftProduct,
        actionData: {
          'productName': 'Carne de Cerdo',
          'batches': 1,
          'rentalCost': 750.0,
          'returnPercentage': 43.5,
          'sellingPricePerUnit': 275.0,
          'salesPercentage': 4.0, // Premium
          'taxPercentage': 2.5,
        },
      ),

      // Paso 7: Crear producto Cerdo Asado
      TutorialStep(
        id: 'create_roasted_pork_product',
        title: 'Definir Cerdo Asado',
        description:
            'Ahora vamos a la cocina para el producto final. Definir Cerdo Asado, valor del objeto 576, cantidad crafteada 10, foco 117, usa 1 carne de cerdo y 36 leche de vaca por lote.',
        action: TutorialAction.createProduct,
        actionData: {
          'productName': 'Cerdo Asado',
          'objectPower': 576,
          'craftedQuantity': 10,
          'focusUsed': 117.0,
          'isRawMaterial': false,
          'ingredients': [
            {'itemName': 'Carne de Cerdo', 'quantity': 1.0},
            {'itemName': 'Leche de Vaca', 'quantity': 36.0},
          ],
        },
      ),

      // Paso 8: Fabricar Cerdo Asado
      TutorialStep(
        id: 'craft_roasted_pork',
        title: 'Fabricar Cerdo Asado',
        description:
            'Fabricar el cerdo asado final. Configurar: 1 lote, alquiler 500, porcentaje de retorno 43.5%, precio de venta 1200.',
        action: TutorialAction.craftProduct,
        actionData: {
          'productName': 'Cerdo Asado',
          'batches': 1,
          'rentalCost': 500.0,
          'returnPercentage': 43.5,
          'sellingPricePerUnit': 1200.0,
          'salesPercentage': 4.0, // Premium
          'taxPercentage': 2.5,
        },
      ),

      // Paso 9: Publicar en el mercado
      TutorialStep(
        id: 'publish_to_market',
        title: 'Publicar en el Mercado',
        description:
            'Ahora vamos a publicar nuestro cerdo asado en el mercado. Observa cómo se descuenta automáticamente el 2.5% de publicación.',
        action: TutorialAction.publishToMarket,
        actionData: {
          'itemName': 'Cerdo Asado',
          'quantity': 10,
          'pricePerUnit': 1200.0,
          'hasPremium': true,
        },
      ),

      // Paso 10: Vender producto
      TutorialStep(
        id: 'sell_product',
        title: 'Registrar Venta',
        description:
            '¡Excelente! Alguien compró nuestro cerdo asado. Vamos a registrar la venta y ver las ganancias.',
        action: TutorialAction.sellProduct,
        actionData: {'quantitySold': 10},
      ),

      // Paso 11: Ver balance
      TutorialStep(
        id: 'view_balance',
        title: 'Revisar Balance',
        description:
            'Vamos a revisar nuestro balance general para ver el resumen de costos, inventario y ganancias.',
        action: TutorialAction.viewBalance,
      ),

      // Paso 12: Ver reportes
      TutorialStep(
        id: 'view_reports',
        title: 'Ver Reportes de Ganancias',
        description:
            'Finalmente, vamos a ver los reportes de ganancias con gráficos para analizar nuestro rendimiento.',
        action: TutorialAction.viewReports,
      ),

      // Paso 13: Finalización
      TutorialStep(
        id: 'tutorial_complete',
        title: '¡El mundo de Albion es tuyo!',
        description:
            '¡Felicitaciones! Has completado exitosamente el tutorial del Cerdo Asado. Ahora conoces todo el proceso desde la cosecha hasta la venta.\n\n🎉 ¡INICIA TU IMPERIO COMERCIAL YA! 🎉\n\nPuedes usar el botón de "Restablecer Balance" en el Balance General para borrar todos los datos del tutorial y empezar con tu propia aventura empresarial en Albion Online.',
      ),
    ];
  }
}
