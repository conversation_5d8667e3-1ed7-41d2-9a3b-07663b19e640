import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class TutorialService {
  static final TutorialService _instance = TutorialService._internal();
  factory TutorialService() => _instance;
  TutorialService._internal();

  // Keys para cada paso del tutorial
  static final GlobalKey fabricarButtonKey = GlobalKey();
  static final GlobalKey menuButtonKey = GlobalKey();
  static final GlobalKey addHarvestKey = GlobalKey();
  static final GlobalKey harvestFormKey = GlobalKey();
  static final GlobalKey saveButtonKey = GlobalKey();
  static final GlobalKey addBreedingKey = GlobalKey();
  static final GlobalKey breedingFormKey = GlobalKey();
  static final GlobalKey addPurchaseKey = GlobalKey();
  static final GlobalKey purchaseFormKey = GlobalKey();
  static final GlobalKey addProductKey = GlobalKey();
  static final GlobalKey productFormKey = GlobalKey();
  static final GlobalKey salesTabKey = GlobalKey();
  static final GlobalKey sellButtonKey = GlobalKey();
  static final GlobalKey balanceTabKey = GlobalKey();

  // Estado del tutorial
  static int _currentStep = 0;
  static bool _tutorialActive = false;

  // Pasos del tutorial
  static const List<String> tutorialSteps = [
    'welcome',
    'fabricar_button',
    'inventory_menu',
    'add_harvest',
    'fill_harvest',
    'save_harvest',
    'add_breeding',
    'fill_breeding',
    'save_breeding',
    'add_purchase',
    'fill_purchase',
    'save_purchase',
    'add_meat_product',
    'fill_meat_product',
    'save_meat_product',
    'add_final_product',
    'fill_final_product',
    'save_final_product',
    'go_to_market',
    'sell_products',
    'view_balance',
    'tutorial_complete',
  ];

  // Datos del tutorial para auto-llenar formularios
  static const Map<String, dynamic> harvestData = {
    'item': 'Maíz',
    'quantity': 152,
    'seedsUsed': 18,
    'seedsReturned': 15,
    'seedPrice': 26010,
    'focusUsed': 0,
  };

  static const Map<String, dynamic> breedingData = {
    'animal': 'Lechón',
    'breedingPrice': 26010,
    'boughtAnimals': 9,
    'bornAnimals': 8,
    'feedItem': 'Maíz',
    'feedPerDay': 9,
    'growthTime': 24,
    'focusUsed': 0,
  };

  static const Map<String, dynamic> purchaseData = {
    'item': 'Leche de Vaca',
    'quantity': 360,
    'unitPrice': 275,
    'marketTax': 2.5,
  };

  static const Map<String, dynamic> meatProcessingData = {
    'product': 'Carne de Cerdo',
    'objectValue': 40,
    'quantityCrafted': 18,
    'rentalCost': 750,
    'returnRate': 43.5,
    'salePrice': 1, // No se vende, va al inventario
    'ingredients': [
      {'name': 'Lechón', 'quantity': 1},
    ],
  };

  static const Map<String, dynamic> finalCookingData = {
    'product': 'Cerdo Asado',
    'objectValue': 576,
    'quantityCrafted': 10,
    'focus': 117,
    'rentalCost': 500,
    'returnRate': 43.5,
    'salePrice': 3800,
    'ingredients': [
      {'name': 'Carne de Cerdo', 'quantity': 72},
      {'name': 'Maíz', 'quantity': 36},
      {'name': 'Leche de Vaca', 'quantity': 36},
    ],
  };

  static const Map<String, dynamic> salesData = {
    'item': 'Cerdo Asado',
    'quantity': 5,
    'unitPrice': 15000,
    'accountType': 'premium',
  };

  // Control del flujo del tutorial
  static void startTutorialFlow() {
    _currentStep = 0;
    _tutorialActive = true;
  }

  static void nextTutorialStep() {
    if (_tutorialActive && _currentStep < tutorialSteps.length - 1) {
      _currentStep++;
    }
  }

  static void completeTutorialFlow() {
    _tutorialActive = false;
    _currentStep = 0;
  }

  static String getCurrentStep() {
    if (!_tutorialActive || _currentStep >= tutorialSteps.length) {
      return '';
    }
    return tutorialSteps[_currentStep];
  }

  static bool isTutorialActive() => _tutorialActive;

  static int getCurrentStepNumber() => _currentStep;

  // Método para continuar el tutorial después de guardar
  static void continueAfterSave(BuildContext context, String stepType) {
    if (!isTutorialActive()) return;

    switch (stepType) {
      case 'harvest':
        if (getCurrentStep() == 'save_harvest') {
          nextTutorialStep(); // Ir a 'add_breeding'
          showTutorialMessage(
            context,
            'Paso 2: Cría de Lechones',
            'Excelente! El maíz está guardado en tu inventario. '
                'Ahora vamos a criar lechones que se alimentarán con el maíz cosechado. '
                'Ve al menú y selecciona "Agregar Cría".',
          );
        }
        break;
      case 'breeding':
        if (getCurrentStep() == 'save_breeding') {
          nextTutorialStep(); // Ir a 'add_purchase'
          showTutorialMessage(
            context,
            'Paso 3: Comprar Leche',
            'Perfecto! Los lechones están creciendo. '
                'Ahora necesitamos comprar leche de vaca en el mercado. '
                'Ve al menú y selecciona "Agregar Compra".',
          );
        }
        break;
      case 'purchase':
        if (getCurrentStep() == 'save_purchase') {
          nextTutorialStep(); // Ir a 'add_meat_product'
          showTutorialMessage(
            context,
            'Paso 4: Procesar Carne',
            'Excelente! La leche está en tu inventario. '
                'Ahora vamos a procesar los lechones para obtener carne de cerdo. '
                'Ve al menú y selecciona "Agregar Producto".',
          );
        }
        break;
    }
  }

  Future<bool> shouldShowTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool('tutorial_completed') ?? false);
  }

  Future<void> markTutorialCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('tutorial_completed', true);
  }

  Future<void> resetTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('tutorial_completed');
  }

  void startTutorial(BuildContext context) {
    ShowCaseWidget.of(context).startShowCase([
      fabricarButtonKey,
      menuButtonKey,
      addHarvestKey,
      harvestFormKey,
      saveButtonKey,
      addBreedingKey,
      breedingFormKey,
      addPurchaseKey,
      purchaseFormKey,
      addProductKey,
      productFormKey,
      salesTabKey,
      sellButtonKey,
      balanceTabKey,
    ]);
  }

  // Métodos para auto-llenar formularios
  static void autoFillHarvestForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController seedsUsedController,
    required TextEditingController seedsReturnedController,
    required TextEditingController seedPriceController,
    required TextEditingController focusController,
  }) {
    itemController.text = harvestData['item'];
    quantityController.text = harvestData['quantity'].toString();
    seedsUsedController.text = harvestData['seedsUsed'].toString();
    seedsReturnedController.text = harvestData['seedsReturned'].toString();
    seedPriceController.text = harvestData['seedPrice'].toString();
    focusController.text = harvestData['focusUsed'].toString();

    // Avanzar al siguiente paso del tutorial
    if (isTutorialActive() && getCurrentStep() == 'fill_harvest') {
      nextTutorialStep(); // Ahora en 'save_harvest'
    }
  }

  static void autoFillBreedingForm({
    required TextEditingController animalController,
    required TextEditingController priceController,
    required TextEditingController boughtController,
    required TextEditingController bornController,
    required TextEditingController feedPerDayController,
    required TextEditingController growthTimeController,
  }) {
    animalController.text = breedingData['animal'];
    priceController.text = breedingData['breedingPrice'].toString();
    boughtController.text = breedingData['boughtAnimals'].toString();
    bornController.text = breedingData['bornAnimals'].toString();
    feedPerDayController.text = breedingData['feedPerDay'].toString();
    growthTimeController.text = breedingData['growthTime'].toString();
  }

  static void autoFillPurchaseForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController unitPriceController,
    required TextEditingController taxController,
  }) {
    itemController.text = purchaseData['item'];
    quantityController.text = purchaseData['quantity'].toString();
    unitPriceController.text = purchaseData['unitPrice'].toString();
    taxController.text = purchaseData['tax'].toString();
  }

  static void autoFillProductForm({
    required TextEditingController productController,
    required TextEditingController quantityController,
    required TextEditingController objectValueController,
    required TextEditingController rentalController,
    required TextEditingController returnRateController,
    required TextEditingController focusController,
    required Map<String, dynamic> productData,
  }) {
    productController.text = productData['product'];
    quantityController.text = productData['quantity'].toString();
    objectValueController.text = productData['objectValue'].toString();
    rentalController.text = productData['rentalCost'].toString();
    returnRateController.text = productData['returnRate'].toString();
    focusController.text = productData['focusUsed'].toString();
  }

  static void autoFillSalesForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController unitPriceController,
    required Function(String) onAccountTypeChanged,
  }) {
    itemController.text = salesData['item'];
    quantityController.text = salesData['quantity'].toString();
    unitPriceController.text = salesData['unitPrice'].toString();
    onAccountTypeChanged(salesData['accountType']);
  }

  // Métodos adicionales para el tutorial completo
  static void autoFillMeatProcessingForm({
    required TextEditingController productController,
    required TextEditingController objectValueController,
    required TextEditingController quantityController,
    required TextEditingController rentalController,
    required TextEditingController returnRateController,
    required TextEditingController salePriceController,
  }) {
    productController.text = meatProcessingData['product'];
    objectValueController.text = meatProcessingData['objectValue'].toString();
    quantityController.text = meatProcessingData['quantityCrafted'].toString();
    rentalController.text = meatProcessingData['rentalCost'].toString();
    returnRateController.text = meatProcessingData['returnRate'].toString();
    salePriceController.text = meatProcessingData['salePrice'].toString();
  }

  static void autoFillFinalCookingForm({
    required TextEditingController productController,
    required TextEditingController objectValueController,
    required TextEditingController quantityController,
    required TextEditingController focusController,
    required TextEditingController rentalController,
    required TextEditingController returnRateController,
    required TextEditingController salePriceController,
  }) {
    productController.text = finalCookingData['product'];
    objectValueController.text = finalCookingData['objectValue'].toString();
    quantityController.text = finalCookingData['quantityCrafted'].toString();
    focusController.text = finalCookingData['focus'].toString();
    rentalController.text = finalCookingData['rentalCost'].toString();
    returnRateController.text = finalCookingData['returnRate'].toString();
    salePriceController.text = finalCookingData['salePrice'].toString();
  }

  // Método para mostrar mensajes del tutorial
  static void showTutorialMessage(
    BuildContext context,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Continuar'),
          ),
        ],
      ),
    );
  }

  // Método para mostrar el mensaje de bienvenida
  static void showWelcomeMessage(BuildContext context) {
    showTutorialMessage(
      context,
      '¡Bienvenido a tu Imperio Comercial!',
      'Hoy inicia tu día como un empresario exitoso en Albion Online. '
          'He traído todos los recursos por ti, ahora vamos a configurar '
          'Albion Online ROI para crear el delicioso Cerdo Asado.',
    );
  }

  // Método para mostrar mensaje de finalización
  static void showCompletionMessage(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('¡Tutorial Completado!'),
        content: const Text(
          '¡Felicitaciones! Has completado el tutorial completo del Cerdo Asado. '
          'Ahora conoces todo el proceso desde la cosecha hasta la venta. '
          '\n\n¡El mundo de Albion es tuyo, inicia tu imperio comercial ya!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Finalizar'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              // Mostrar opción de resetear balance
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Resetear Balance'),
                  content: const Text(
                    '¿Quieres resetear el balance para empezar con tus propios productos?',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('No'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Aquí se implementaría el reset del balance
                      },
                      child: const Text('Sí, resetear'),
                    ),
                  ],
                ),
              );
            },
            child: const Text('Resetear Balance'),
          ),
        ],
      ),
    );
  }
}
