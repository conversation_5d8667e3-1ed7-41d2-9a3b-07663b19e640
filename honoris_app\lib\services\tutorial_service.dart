import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class TutorialService {
  static final TutorialService _instance = TutorialService._internal();
  factory TutorialService() => _instance;
  TutorialService._internal();

  // Keys para cada paso del tutorial
  static final GlobalKey fabricarButtonKey = GlobalKey();
  static final GlobalKey menuButtonKey = GlobalKey();
  static final GlobalKey addHarvestKey = GlobalKey();
  static final GlobalKey harvestFormKey = GlobalKey();
  static final GlobalKey saveButtonKey = GlobalKey();
  static final GlobalKey addBreedingKey = GlobalKey();
  static final GlobalKey breedingFormKey = GlobalKey();
  static final GlobalKey addPurchaseKey = GlobalKey();
  static final GlobalKey purchaseFormKey = GlobalKey();
  static final GlobalKey addProductKey = GlobalKey();
  static final GlobalKey productFormKey = GlobalKey();
  static final GlobalKey salesTabKey = GlobalKey();
  static final GlobalKey sellButtonKey = GlobalKey();
  static final GlobalKey balanceTabKey = GlobalKey();

  // Datos del tutorial para auto-llenar formularios
  static const Map<String, dynamic> harvestData = {
    'item': 'Maíz',
    'quantity': 152,
    'seedsUsed': 18,
    'seedsReturned': 15,
    'seedPrice': 26010,
    'focusUsed': 0,
  };

  static const Map<String, dynamic> breedingData = {
    'item': 'Cerdo Bebé',
    'quantity': 8,
    'animalsUsed': 9,
    'focusUsed': 0,
  };

  static const Map<String, dynamic> purchaseData = {
    'item': 'Leche',
    'quantity': 10,
    'unitPrice': 275,
    'tax': 2.5,
  };

  static const Map<String, dynamic> meatProcessingData = {
    'product': 'Carne de Cerdo',
    'quantity': 18,
    'objectValue': 40,
    'rentalCost': 750,
    'returnRate': 43.5,
    'focusUsed': 0,
  };

  static const Map<String, dynamic> finalCookingData = {
    'product': 'Cerdo Asado',
    'quantity': 10,
    'objectValue': 576,
    'rentalCost': 500,
    'returnRate': 43.5,
    'focusUsed': 117,
  };

  static const Map<String, dynamic> salesData = {
    'item': 'Cerdo Asado',
    'quantity': 5,
    'unitPrice': 15000,
    'accountType': 'premium',
  };

  Future<bool> shouldShowTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool('tutorial_completed') ?? false);
  }

  Future<void> markTutorialCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('tutorial_completed', true);
  }

  Future<void> resetTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('tutorial_completed');
  }

  void startTutorial(BuildContext context) {
    ShowCaseWidget.of(context).startShowCase([
      fabricarButtonKey,
      menuButtonKey,
      addHarvestKey,
      harvestFormKey,
      saveButtonKey,
      addBreedingKey,
      breedingFormKey,
      addPurchaseKey,
      purchaseFormKey,
      addProductKey,
      productFormKey,
      salesTabKey,
      sellButtonKey,
      balanceTabKey,
    ]);
  }

  // Métodos para auto-llenar formularios
  static void autoFillHarvestForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController seedsUsedController,
    required TextEditingController seedsReturnedController,
    required TextEditingController seedPriceController,
    required TextEditingController focusController,
  }) {
    itemController.text = harvestData['item'];
    quantityController.text = harvestData['quantity'].toString();
    seedsUsedController.text = harvestData['seedsUsed'].toString();
    seedsReturnedController.text = harvestData['seedsReturned'].toString();
    seedPriceController.text = harvestData['seedPrice'].toString();
    focusController.text = harvestData['focusUsed'].toString();
  }

  static void autoFillBreedingForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController animalsUsedController,
    required TextEditingController focusController,
  }) {
    itemController.text = breedingData['item'];
    quantityController.text = breedingData['quantity'].toString();
    animalsUsedController.text = breedingData['animalsUsed'].toString();
    focusController.text = breedingData['focusUsed'].toString();
  }

  static void autoFillPurchaseForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController unitPriceController,
    required TextEditingController taxController,
  }) {
    itemController.text = purchaseData['item'];
    quantityController.text = purchaseData['quantity'].toString();
    unitPriceController.text = purchaseData['unitPrice'].toString();
    taxController.text = purchaseData['tax'].toString();
  }

  static void autoFillProductForm({
    required TextEditingController productController,
    required TextEditingController quantityController,
    required TextEditingController objectValueController,
    required TextEditingController rentalController,
    required TextEditingController returnRateController,
    required TextEditingController focusController,
    required Map<String, dynamic> productData,
  }) {
    productController.text = productData['product'];
    quantityController.text = productData['quantity'].toString();
    objectValueController.text = productData['objectValue'].toString();
    rentalController.text = productData['rentalCost'].toString();
    returnRateController.text = productData['returnRate'].toString();
    focusController.text = productData['focusUsed'].toString();
  }

  static void autoFillSalesForm({
    required TextEditingController itemController,
    required TextEditingController quantityController,
    required TextEditingController unitPriceController,
    required Function(String) onAccountTypeChanged,
  }) {
    itemController.text = salesData['item'];
    quantityController.text = salesData['quantity'].toString();
    unitPriceController.text = salesData['unitPrice'].toString();
    onAccountTypeChanged(salesData['accountType']);
  }
}
