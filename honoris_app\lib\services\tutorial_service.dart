import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/tutorial_step.dart';

class TutorialService {
  static const String _progressKey = 'tutorial_progress';
  static const String _hasSeenTutorialKey = 'has_seen_tutorial';

  /// Obtiene el progreso actual del tutorial
  static Future<TutorialProgress?> getTutorialProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_progressKey);
      
      if (progressJson == null) return null;
      
      final progressMap = json.decode(progressJson);
      return TutorialProgress.fromJson(progressMap);
    } catch (e) {
      return null;
    }
  }

  /// Guarda el progreso del tutorial
  static Future<bool> saveTutorialProgress(TutorialProgress progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = json.encode(progress.toJson());
      return await prefs.setString(_progressKey, progressJson);
    } catch (e) {
      return false;
    }
  }

  /// Inicia un nuevo tutorial
  static Future<bool> startTutorial() async {
    final progress = TutorialProgress(
      currentStepId: 'welcome',
      completedSteps: [],
      isCompleted: false,
      startedAt: DateTime.now(),
    );
    return await saveTutorialProgress(progress);
  }

  /// Marca un paso como completado y avanza al siguiente
  static Future<bool> completeStep(String stepId, String nextStepId) async {
    try {
      final currentProgress = await getTutorialProgress();
      if (currentProgress == null) return false;

      final updatedCompletedSteps = List<String>.from(currentProgress.completedSteps);
      if (!updatedCompletedSteps.contains(stepId)) {
        updatedCompletedSteps.add(stepId);
      }

      final isLastStep = nextStepId == 'completed';
      final updatedProgress = currentProgress.copyWith(
        currentStepId: isLastStep ? 'completed' : nextStepId,
        completedSteps: updatedCompletedSteps,
        isCompleted: isLastStep,
        completedAt: isLastStep ? DateTime.now() : null,
      );

      return await saveTutorialProgress(updatedProgress);
    } catch (e) {
      return false;
    }
  }

  /// Verifica si el usuario ya vio el tutorial
  static Future<bool> hasSeenTutorial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_hasSeenTutorialKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Marca que el usuario ya vio el tutorial
  static Future<bool> markTutorialAsSeen() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_hasSeenTutorialKey, true);
    } catch (e) {
      return false;
    }
  }

  /// Reinicia el tutorial
  static Future<bool> resetTutorial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_progressKey);
      await prefs.remove(_hasSeenTutorialKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Obtiene todos los pasos del tutorial del cerdo asado
  static List<TutorialStep> getCerdoAsadoTutorialSteps() {
    return [
      // Paso 1: Bienvenida
      TutorialStep(
        id: 'welcome',
        title: '¡Bienvenido Empresario!',
        description: 'Hoy inicia tu día como un empresario exitoso en Albion Online. He traído todos los recursos por ti, ahora vamos a configurar Albion Online ROI para crear el delicioso Cerdo Asado.',
      ),
      
      // Paso 2: Configurar cosecha de maíz
      TutorialStep(
        id: 'add_corn_harvest',
        title: 'Configurar Cosecha de Maíz',
        description: 'Tienes 2 parcelas de maíz en tu isla. Cada semilla de maíz cuesta 26.010 y se compró en la isla (0% de tasa). Se compraron 18 semillas, se recibieron 15 con 152 maíz.',
        action: TutorialAction.addHarvest,
        actionData: {
          'itemName': 'Maíz',
          'seedPrice': 26010.0,
          'seedsPurchased': 18,
          'seedsReceived': 15,
          'harvestReceived': 152,
          'focusPerSeed': 0.0,
          'isMarketPurchase': false,
        },
      ),

      // Paso 3: Configurar cría de lechón
      TutorialStep(
        id: 'add_pig_breeding',
        title: 'Configurar Cría de Lechón',
        description: 'Tienes 1 parcela de cerdos. Las crías de cerdos (lechón) se compraron ayer en la isla por 26.010 y se alimentan con el maíz. De los 9 cerdos solo nacieron 8 crías.',
        action: TutorialAction.addBreeding,
        actionData: {
          'animalName': 'Lechón',
          'breedingPrice': 26010.0,
          'animalsPurchased': 9,
          'animalsBorn': 8,
          'foodItem': 'Maíz',
          'foodPerDay': 9,
          'growthTime': 24,
          'isMarketPurchase': false,
        },
      ),

      // Paso 4: Comprar leche de vaca
      TutorialStep(
        id: 'add_milk_purchase',
        title: 'Comprar Leche de Vaca',
        description: 'Nos falta la leche que se comprará en el mercado. La leche de vaca se compró en el mercado pagando 2.5% de tasa, se compraron 360 unidades por un precio de 275.',
        action: TutorialAction.addDirectPurchase,
        actionData: {
          'itemName': 'Leche de Vaca',
          'originalPrice': 275.0,
          'quantity': 360,
          'isMarketPurchase': true,
        },
      ),

      // Paso 5: Crear producto Carne de Cerdo
      TutorialStep(
        id: 'create_pork_product',
        title: 'Definir Carne de Cerdo',
        description: 'Vamos a la carnicería a picar los cerdos. Definir Carne de cerdo, valor del objeto 40, cantidad crafteada 18, es materia prima, y usa 1 lechón por cada lote.',
        action: TutorialAction.createProduct,
        actionData: {
          'productName': 'Carne de Cerdo',
          'objectPower': 40,
          'craftedQuantity': 18,
          'isRawMaterial': true,
          'ingredients': [
            {'itemName': 'Lechón', 'quantity': 1.0}
          ],
        },
      ),
    ];
  }
}
