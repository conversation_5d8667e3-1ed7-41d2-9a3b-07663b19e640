import 'dart:math';
import '../models/tier.dart';
import '../models/recipe.dart';
import '../models/calculation_config.dart';
import '../models/calculation_result.dart';

class CalculationService {
  /// Calcula la cantidad total de cuero producido dado una cantidad inicial de pieles en bruto
  /// de ese tier y la receta.
  static int calculateLeatherFromRawHides(
    int initialRawHides,
    double returnPercentage,
    int reqRawPerCraft,
  ) {
    if (initialRawHides <= 0 || reqRawPerCraft <= 0) return 0;

    final returnFraction = returnPercentage / 100;
    double hidesAvailable = initialRawHides.toDouble();
    int leatherProduced = 0;

    while (hidesAvailable >= reqRawPerCraft) {
      final craftableThisRound = (hidesAvailable / reqRawPerCraft).floor();
      if (craftableThisRound < 1) break;

      leatherProduced += craftableThisRound;
      final consumedHides = craftableThisRound * reqRawPerCraft;
      hidesAvailable -= consumedHides;

      final returnedHides = consumedHides * returnFraction;
      hidesAvailable += returnedHides;
    }
    return leatherProduced;
  }

  /// Simula el refinamiento binario de un material de input (cuero de tier anterior)
  /// para producir el ítem del tier actual.
  static int simulateTierProductionFromPrevious(
    int availablePrevMaterial,
    int reqPrevPerCraft,
    double returnPercentage,
  ) {
    if (availablePrevMaterial <= 0 || reqPrevPerCraft <= 0) return 0;

    final returnFraction = returnPercentage / 100;
    double materialAvailable = availablePrevMaterial.toDouble();
    int itemsProduced = 0;

    while (materialAvailable >= reqPrevPerCraft) {
      final craftableThisRound = (materialAvailable / reqPrevPerCraft).floor();
      if (craftableThisRound < 1) break;

      itemsProduced += craftableThisRound;
      final consumedMaterial = craftableThisRound * reqPrevPerCraft;
      materialAvailable -= consumedMaterial;

      final returnedMaterial = consumedMaterial * returnFraction;
      materialAvailable += returnedMaterial;
    }
    return itemsProduced;
  }

  /// Calcula la cantidad mínima de un material de input necesaria para craftear una cantidad objetivo
  /// de un ítem que requiere ese material, considerando el retorno sobre el consumo de ese material.
  static int calculateRequiredInputWithReturn(
    int targetOutputQuantity,
    int requiredPerCraft,
    double returnPercentage,
  ) {
    if (targetOutputQuantity <= 0 || requiredPerCraft <= 0) return 0;

    final returnFraction = returnPercentage / 100;

    bool checkCanProduce(int initialInput) {
      double inputAvailable = initialInput.toDouble();
      int outputProduced = 0;

      while (inputAvailable >= requiredPerCraft) {
        final canCraftThisRound = (inputAvailable / requiredPerCraft).floor();
        final craftThisRound = min(
          canCraftThisRound,
          targetOutputQuantity - outputProduced,
        );

        if (craftThisRound <= 0) break;

        outputProduced += craftThisRound;
        final consumedInput = craftThisRound * requiredPerCraft;
        inputAvailable -= consumedInput;
        final returnedInput = consumedInput * returnFraction;
        inputAvailable += returnedInput;

        if (outputProduced >= targetOutputQuantity) return true;
      }
      return outputProduced >= targetOutputQuantity;
    }

    // Ajustar los límites de búsqueda para evitar bucles infinitos
    int low = max(
      0,
      (targetOutputQuantity * requiredPerCraft * (1 - returnFraction)).ceil() -
          1,
    );
    int high = targetOutputQuantity * requiredPerCraft * 2;

    // Asegurarse de que el límite superior inicial sea suficiente
    int iterations = 0;
    const maxIterations = 20;
    while (!checkCanProduce(high) && iterations < maxIterations) {
      high = high * 2;
      if (high > 9007199254740991) {
        // Number.MAX_SAFE_INTEGER equivalent
        return (targetOutputQuantity * requiredPerCraft).ceil();
      }
      iterations++;
    }
    if (iterations >= maxIterations && !checkCanProduce(high)) {
      return (targetOutputQuantity * requiredPerCraft).ceil();
    }

    int minRequired = high;

    // Búsqueda binaria para encontrar la cantidad mínima requerida
    int searchLow = low;
    int searchHigh = high;
    iterations = 0;

    while (searchLow <= searchHigh && iterations < 100) {
      final mid = ((searchLow + searchHigh) / 2).floor();
      if (mid < 0) {
        searchLow = 0;
        continue;
      }
      if (checkCanProduce(mid)) {
        minRequired = mid;
        searchHigh = mid - 1;
      } else {
        searchLow = mid + 1;
      }
      iterations++;
    }

    return minRequired.ceil();
  }

  /// Realiza todos los cálculos de refinado basados en la configuración proporcionada
  static CalculationResult performCalculations(CalculationConfig config) {
    final purchaseMultiplier = 1 + (config.purchasePercentage / 100);
    // Solo aplicar el impuesto de mercado (el de publicación ya se descontó al publicar)
    final netSellingPriceMultiplier = 1 - (config.salesPercentage / 100);

    // Inicializar mapas para almacenar resultados
    final craftedQuantity = <Tier, int>{for (var tier in Tier.values) tier: 0};
    final requiredRawHides = <Tier, int>{for (var tier in Tier.values) tier: 0};
    final requiredLeather = <Tier, int>{for (var tier in Tier.values) tier: 0};
    final totalCostPerTier = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };
    final costPerUnit = <Tier, double>{for (var tier in Tier.values) tier: 0.0};
    final netSellingPricePerTier = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };
    final profitLossAmountPerTier = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };
    final profitLossPercentagePerTier = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };
    final tierStatus = <Tier, TierStatus>{
      for (var tier in Tier.values) tier: TierStatus.notCrafted,
    };
    final totalRentalCostPerTier = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };
    final rentalCostPerUnit = <Tier, double>{
      for (var tier in Tier.values) tier: 0.0,
    };

    final startIndex = Tier.values.indexOf(config.startingTier);
    final limitIndex = Tier.values.indexOf(config.craftingLimitTier);
    int availableLeatherFromPreviousTier = 0;

    // Procesar cada tier desde el inicial hasta el límite
    for (int i = startIndex; i <= limitIndex; i++) {
      final currentTier = Tier.values[i];
      final recipe = Recipe.getRecipe(currentTier);
      if (recipe == null) {
        tierStatus[currentTier] = TierStatus.notCrafted;
        continue;
      }

      final prevTier = recipe.previousTier;
      final reqPrev = recipe.requiredPrevious;
      final reqRaw = recipe.requiredRaw;
      final rawCost =
          (config.rawMaterialCosts[currentTier] ?? 0) * purchaseMultiplier;

      netSellingPricePerTier[currentTier] =
          (config.sellingPrices[currentTier] ?? 0) * netSellingPriceMultiplier;

      double costOfInputMaterialsForTier = 0;

      final startingTierIndex = startIndex;
      final prevTierIndex = (prevTier != null)
          ? Tier.values.indexOf(prevTier)
          : -1;

      if (currentTier == config.startingTier) {
        // Procesar tier inicial
        final initialRawHidesForStartingTier = config.initialQuantity;
        final producedStartingLeather = calculateLeatherFromRawHides(
          initialRawHidesForStartingTier,
          config.returnPercentage,
          reqRaw,
        );
        craftedQuantity[currentTier] = producedStartingLeather;

        if (craftedQuantity[currentTier]! <= 0) {
          availableLeatherFromPreviousTier = 0;
          tierStatus[currentTier] = TierStatus.notCrafted;
          continue;
        }
        requiredRawHides[currentTier] = initialRawHidesForStartingTier;

        if (startingTierIndex > 0 && prevTier != null && reqPrev > 0) {
          requiredLeather[currentTier] = calculateRequiredInputWithReturn(
            craftedQuantity[currentTier]!,
            reqPrev,
            config.returnPercentage,
          );
        } else {
          requiredLeather[currentTier] = 0;
        }

        final costOfConsumedRawHides = initialRawHidesForStartingTier * rawCost;
        double costOfConsumedLeather = 0;
        if (startingTierIndex > 0 && prevTier != null && reqPrev > 0) {
          costOfConsumedLeather =
              (requiredLeather[currentTier] ?? 0) *
              (config.buyingPrices[prevTier] ?? 0);
        }
        costOfInputMaterialsForTier =
            costOfConsumedRawHides + costOfConsumedLeather;
        availableLeatherFromPreviousTier = craftedQuantity[currentTier]!;
      } else {
        // Procesar tiers subsecuentes
        final availablePrevTierLeatherForCrafting =
            availableLeatherFromPreviousTier;
        if (availablePrevTierLeatherForCrafting < reqPrev && reqPrev > 0) {
          availableLeatherFromPreviousTier = 0;
          tierStatus[currentTier] = TierStatus.notCrafted;
          continue;
        }

        craftedQuantity[currentTier] = simulateTierProductionFromPrevious(
          availablePrevTierLeatherForCrafting,
          reqPrev,
          config.returnPercentage,
        );

        if (craftedQuantity[currentTier]! <= 0) {
          availableLeatherFromPreviousTier = 0;
          tierStatus[currentTier] = TierStatus.notCrafted;
          continue;
        }

        requiredRawHides[currentTier] = calculateRequiredInputWithReturn(
          craftedQuantity[currentTier]!,
          reqRaw,
          config.returnPercentage,
        );

        if (reqPrev > 0) {
          requiredLeather[currentTier] = calculateRequiredInputWithReturn(
            craftedQuantity[currentTier]!,
            reqPrev,
            config.returnPercentage,
          );
        } else {
          requiredLeather[currentTier] = 0;
        }

        double costOfConsumedLeather = 0;
        if (prevTierIndex != -1 &&
            prevTierIndex < startingTierIndex &&
            reqPrev > 0) {
          costOfConsumedLeather =
              (requiredLeather[currentTier] ?? 0) *
              (config.buyingPrices[prevTier] ?? 0);
        } else if (prevTier != null && reqPrev > 0) {
          costOfConsumedLeather =
              (requiredLeather[currentTier] ?? 0) *
              (costPerUnit[prevTier] ?? 0);
        }
        final costOfConsumedRawHides =
            (requiredRawHides[currentTier] ?? 0) * rawCost;
        costOfInputMaterialsForTier =
            costOfConsumedLeather + costOfConsumedRawHides;
        availableLeatherFromPreviousTier = craftedQuantity[currentTier]!;
      }

      // Calcular costos de alquiler
      final buildingValue = BuildingValues.getValue(currentTier);
      final rentalCostForTier = (craftedQuantity[currentTier]! > 0)
          ? (config.rentalCost / 100) *
                buildingValue *
                0.1125 *
                craftedQuantity[currentTier]!
          : 0.0;
      totalRentalCostPerTier[currentTier] = rentalCostForTier;
      rentalCostPerUnit[currentTier] = (craftedQuantity[currentTier]! > 0)
          ? rentalCostForTier / craftedQuantity[currentTier]!
          : 0.0;

      totalCostPerTier[currentTier] =
          costOfInputMaterialsForTier + totalRentalCostPerTier[currentTier]!;
      costPerUnit[currentTier] = (craftedQuantity[currentTier]! > 0)
          ? totalCostPerTier[currentTier]! / craftedQuantity[currentTier]!
          : 0.0;

      profitLossAmountPerTier[currentTier] =
          (netSellingPricePerTier[currentTier]! -
              (costPerUnit[currentTier] ?? 0)) *
          craftedQuantity[currentTier]!;

      final investmentForPercentage = totalCostPerTier[currentTier] ?? 0;

      if (investmentForPercentage == 0) {
        profitLossPercentagePerTier[currentTier] =
            profitLossAmountPerTier[currentTier]! > 0 ? double.infinity : 0;
      } else {
        profitLossPercentagePerTier[currentTier] =
            (profitLossAmountPerTier[currentTier]! / investmentForPercentage) *
            100;
      }

      if (craftedQuantity[currentTier]! > 0) {
        tierStatus[currentTier] =
            ((netSellingPricePerTier[currentTier] ?? 0) >=
                (costPerUnit[currentTier] ?? 0))
            ? TierStatus.profit
            : TierStatus.loss;
      } else {
        tierStatus[currentTier] = TierStatus.notCrafted;
      }
    }

    // Calcular resumen
    double totalRawHideCostSummary = 0;
    double totalAcquiredLeatherCostSummary = 0;
    double totalRentalCostSummary = 0;

    final resultTiers = Tier.values.sublist(startIndex, limitIndex + 1);

    // Calcular costos totales de materias primas
    totalRawHideCostSummary +=
        config.initialQuantity *
        ((config.rawMaterialCosts[config.startingTier] ?? 0) *
            purchaseMultiplier);

    for (int i = startIndex + 1; i <= limitIndex; i++) {
      final currentTier = Tier.values[i];
      if ((craftedQuantity[currentTier] ?? 0) > 0) {
        totalRawHideCostSummary +=
            (requiredRawHides[currentTier] ?? 0) *
            ((config.rawMaterialCosts[currentTier] ?? 0) * purchaseMultiplier);
      }
    }

    // Calcular costos de cuero adquirido
    for (int i = startIndex; i <= limitIndex; i++) {
      final currentTier = Tier.values[i];
      final recipe = Recipe.getRecipe(currentTier);
      if (recipe == null) continue;

      final prevTier = recipe.previousTier;
      final prevTierIdx = prevTier != null ? Tier.values.indexOf(prevTier) : -1;
      final startingTierIdx = Tier.values.indexOf(config.startingTier);

      if ((craftedQuantity[currentTier] ?? 0) > 0 &&
          prevTierIdx != -1 &&
          prevTierIdx < startingTierIdx &&
          prevTier != null) {
        totalAcquiredLeatherCostSummary +=
            (requiredLeather[currentTier] ?? 0) *
            (config.buyingPrices[prevTier] ?? 0);
      }
    }

    // Calcular costos de alquiler totales
    for (final tier in resultTiers) {
      if ((craftedQuantity[tier] ?? 0) > 0) {
        totalRentalCostSummary += totalRentalCostPerTier[tier] ?? 0;
      }
    }

    final totalMaterialInvestmentSummary =
        totalRawHideCostSummary + totalAcquiredLeatherCostSummary;
    final finalTier = config.craftingLimitTier;
    final totalRevenue =
        (craftedQuantity[finalTier] ?? 0) *
        (netSellingPricePerTier[finalTier] ?? 0);
    final netProfitLossSummary =
        totalRevenue - totalMaterialInvestmentSummary - totalRentalCostSummary;
    final totalInvestmentForPercentageOverall =
        totalMaterialInvestmentSummary + totalRentalCostSummary;

    double netProfitLossPercentageSummary = 0;
    if (totalInvestmentForPercentageOverall > 0) {
      netProfitLossPercentageSummary =
          (netProfitLossSummary / totalInvestmentForPercentageOverall) * 100;
    } else if (netProfitLossSummary > 0) {
      netProfitLossPercentageSummary = double.infinity;
    }

    // Crear resultados por tier
    final tierResults = <Tier, TierResult>{};
    for (final tier in resultTiers) {
      final prevTier = tier.previous;
      final startingTierIdx = Tier.values.indexOf(config.startingTier);
      final prevTierIdx = prevTier != null ? Tier.values.indexOf(prevTier) : -1;

      double buyingPriceForDisplay = 0;
      if (prevTierIdx != -1 &&
          prevTierIdx < startingTierIdx &&
          prevTier != null) {
        buyingPriceForDisplay = config.buyingPrices[prevTier] ?? 0;
      }

      tierResults[tier] = TierResult(
        quantity: craftedQuantity[tier] ?? 0,
        requiredRawHides: requiredRawHides[tier] ?? 0,
        requiredLeather: requiredLeather[tier] ?? 0,
        costPerUnit: double.parse((costPerUnit[tier] ?? 0).toStringAsFixed(2)),
        rentalCostPerUnit: double.parse(
          (rentalCostPerUnit[tier] ?? 0).toStringAsFixed(2),
        ),
        sellingPrice: config.sellingPrices[tier] ?? 0,
        netSellingPrice: double.parse(
          (netSellingPricePerTier[tier] ?? 0).toStringAsFixed(2),
        ),
        buyingPricePrevTierMaterial: double.parse(
          buyingPriceForDisplay.toStringAsFixed(2),
        ),
        profitLossAmount: double.parse(
          (profitLossAmountPerTier[tier] ?? 0).toStringAsFixed(2),
        ),
        profitLossPercentage: double.parse(
          (profitLossPercentagePerTier[tier] ?? 0).toStringAsFixed(2),
        ),
        status: tierStatus[tier] ?? TierStatus.notCrafted,
      );
    }

    final summary = CalculationSummary(
      totalRawHideCost: double.parse(
        totalRawHideCostSummary.toStringAsFixed(2),
      ),
      totalAcquiredLeatherCost: double.parse(
        totalAcquiredLeatherCostSummary.toStringAsFixed(2),
      ),
      totalMaterialInvestment: double.parse(
        totalMaterialInvestmentSummary.toStringAsFixed(2),
      ),
      totalRentalCost: double.parse(totalRentalCostSummary.toStringAsFixed(2)),
      totalRevenue: double.parse(totalRevenue.toStringAsFixed(2)),
      netProfitLoss: double.parse(netProfitLossSummary.toStringAsFixed(2)),
      netProfitLossPercentage: double.parse(
        netProfitLossPercentageSummary.toStringAsFixed(2),
      ),
    );

    return CalculationResult(summary: summary, tierResults: tierResults);
  }
}
