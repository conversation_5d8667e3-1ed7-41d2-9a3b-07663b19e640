﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}"
	ProjectSection(ProjectDependencies) = postProject
		{CF078630-E217-3E50-9E96-6530F12143B5} = {CF078630-E217-3E50-9E96-6530F12143B5}
		{6D74745A-3347-3F13-94FC-233ECDE464B1} = {6D74745A-3347-3F13-94FC-233ECDE464B1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{997A75E9-391A-3AA8-8664-81CE47656184}"
	ProjectSection(ProjectDependencies) = postProject
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B} = {E8D87463-86E9-3FA6-B2FF-98F5B40D802B}
		{CF078630-E217-3E50-9E96-6530F12143B5} = {CF078630-E217-3E50-9E96-6530F12143B5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{CF078630-E217-3E50-9E96-6530F12143B5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{BD9F5E1A-780A-3325-8D3E-3A676B39653A}"
	ProjectSection(ProjectDependencies) = postProject
		{CF078630-E217-3E50-9E96-6530F12143B5} = {CF078630-E217-3E50-9E96-6530F12143B5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}"
	ProjectSection(ProjectDependencies) = postProject
		{CF078630-E217-3E50-9E96-6530F12143B5} = {CF078630-E217-3E50-9E96-6530F12143B5}
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A} = {BD9F5E1A-780A-3325-8D3E-3A676B39653A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "url_launcher_windows_plugin", "url_launcher_windows_plugin.vcxproj", "{6D74745A-3347-3F13-94FC-233ECDE464B1}"
	ProjectSection(ProjectDependencies) = postProject
		{CF078630-E217-3E50-9E96-6530F12143B5} = {CF078630-E217-3E50-9E96-6530F12143B5}
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A} = {BD9F5E1A-780A-3325-8D3E-3A676B39653A}
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880} = {696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Debug|x64.ActiveCfg = Debug|x64
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Debug|x64.Build.0 = Debug|x64
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Profile|x64.ActiveCfg = Profile|x64
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Profile|x64.Build.0 = Profile|x64
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Release|x64.ActiveCfg = Release|x64
		{E8D87463-86E9-3FA6-B2FF-98F5B40D802B}.Release|x64.Build.0 = Release|x64
		{997A75E9-391A-3AA8-8664-81CE47656184}.Debug|x64.ActiveCfg = Debug|x64
		{997A75E9-391A-3AA8-8664-81CE47656184}.Profile|x64.ActiveCfg = Profile|x64
		{997A75E9-391A-3AA8-8664-81CE47656184}.Release|x64.ActiveCfg = Release|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Debug|x64.ActiveCfg = Debug|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Debug|x64.Build.0 = Debug|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Profile|x64.ActiveCfg = Profile|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Profile|x64.Build.0 = Profile|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Release|x64.ActiveCfg = Release|x64
		{CF078630-E217-3E50-9E96-6530F12143B5}.Release|x64.Build.0 = Release|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Debug|x64.ActiveCfg = Debug|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Debug|x64.Build.0 = Debug|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Profile|x64.ActiveCfg = Profile|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Profile|x64.Build.0 = Profile|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Release|x64.ActiveCfg = Release|x64
		{BD9F5E1A-780A-3325-8D3E-3A676B39653A}.Release|x64.Build.0 = Release|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Debug|x64.ActiveCfg = Debug|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Debug|x64.Build.0 = Debug|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Profile|x64.ActiveCfg = Profile|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Profile|x64.Build.0 = Profile|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Release|x64.ActiveCfg = Release|x64
		{696CCEC0-D48A-3AAF-BEBB-E9ECE9D70880}.Release|x64.Build.0 = Release|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Debug|x64.ActiveCfg = Debug|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Debug|x64.Build.0 = Debug|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Profile|x64.ActiveCfg = Profile|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Profile|x64.Build.0 = Profile|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Release|x64.ActiveCfg = Release|x64
		{6D74745A-3347-3F13-94FC-233ECDE464B1}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {66A007B2-A32B-3179-8567-1BABFE68512F}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
