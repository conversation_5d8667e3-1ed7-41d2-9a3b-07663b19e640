// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calculation_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CalculationConfig _$CalculationConfigFromJson(Map<String, dynamic> json) =>
    CalculationConfig(
      rentalCost: (json['rentalCost'] as num).toDouble(),
      purchasePercentage: (json['purchasePercentage'] as num).toDouble(),
      salesPercentage: (json['salesPercentage'] as num).toDouble(),
      taxPercentage: (json['taxPercentage'] as num).toDouble(),
      returnPercentage: (json['returnPercentage'] as num).toDouble(),
      startingTier: $enumDecode(_$TierEnumMap, json['startingTier']),
      craftingLimitTier: $enumDecode(_$TierEnumMap, json['craftingLimitTier']),
      initialQuantity: (json['initialQuantity'] as num).toInt(),
      rawMaterialCosts: (json['rawMaterialCosts'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry($enumDecode(_$TierEnumMap, k), (e as num).toDouble()),
      ),
      sellingPrices: (json['sellingPrices'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry($enumDecode(_$TierEnumMap, k), (e as num).toDouble()),
      ),
      buyingPrices: (json['buyingPrices'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry($enumDecode(_$TierEnumMap, k), (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$CalculationConfigToJson(CalculationConfig instance) =>
    <String, dynamic>{
      'rentalCost': instance.rentalCost,
      'purchasePercentage': instance.purchasePercentage,
      'salesPercentage': instance.salesPercentage,
      'taxPercentage': instance.taxPercentage,
      'returnPercentage': instance.returnPercentage,
      'startingTier': _$TierEnumMap[instance.startingTier]!,
      'craftingLimitTier': _$TierEnumMap[instance.craftingLimitTier]!,
      'initialQuantity': instance.initialQuantity,
      'rawMaterialCosts': instance.rawMaterialCosts.map(
        (k, e) => MapEntry(_$TierEnumMap[k]!, e),
      ),
      'sellingPrices': instance.sellingPrices.map(
        (k, e) => MapEntry(_$TierEnumMap[k]!, e),
      ),
      'buyingPrices': instance.buyingPrices.map(
        (k, e) => MapEntry(_$TierEnumMap[k]!, e),
      ),
    };

const _$TierEnumMap = {
  Tier.t2: 't2',
  Tier.t3: 't3',
  Tier.t4: 't4',
  Tier.t5: 't5',
  Tier.t6: 't6',
  Tier.t7: 't7',
  Tier.t8: 't8',
};
