enum Tier {
  t2('T2'),
  t3('T3'),
  t4('T4'),
  t5('T5'),
  t6('T6'),
  t7('T7'),
  t8('T8');

  const Tier(this.displayName);
  
  final String displayName;

  static Tier fromString(String value) {
    switch (value.toLowerCase()) {
      case 't2':
        return Tier.t2;
      case 't3':
        return Tier.t3;
      case 't4':
        return Tier.t4;
      case 't5':
        return Tier.t5;
      case 't6':
        return Tier.t6;
      case 't7':
        return Tier.t7;
      case 't8':
        return Tier.t8;
      default:
        throw ArgumentError('Invalid tier: $value');
    }
  }

  String get value => name;

  Tier? get previous {
    final index = Tier.values.indexOf(this);
    if (index > 0) {
      return Tier.values[index - 1];
    }
    return null;
  }

  Tier? get next {
    final index = Tier.values.indexOf(this);
    if (index < Tier.values.length - 1) {
      return Tier.values[index + 1];
    }
    return null;
  }

  static List<Tier> get availableStartTiers => 
      Tier.values.sublist(0, Tier.values.length - 1); // T2 to T7

  static List<Tier> get availableLimitTiers => 
      Tier.values; // T2 to T8
}
