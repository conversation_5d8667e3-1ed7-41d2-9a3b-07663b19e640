import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/calculation_provider.dart';
import 'providers/crafting_provider.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(const HonorisApp());
}

class HonorisApp extends StatelessWidget {
  const HonorisApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => CalculationProvider()),
        ChangeNotifierProvider(create: (context) => CraftingProvider()),
      ],
      child: MaterialApp(
        title: 'Honoris - Calculadora de Refinado',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(
              0xFF2563EB,
            ), // Blue color similar to web version
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(centerTitle: true, elevation: 2),
          cardTheme: CardThemeData(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: Colors.grey[50],
          ),
        ),
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
