// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tutorial_step.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TutorialStep _$TutorialStepFromJson(Map<String, dynamic> json) => TutorialStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imagePath: json['imagePath'] as String?,
      action: $enumDecodeNullable(_$TutorialActionEnumMap, json['action']),
      actionData: json['actionData'] as Map<String, dynamic>?,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$TutorialStepToJson(TutorialStep instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'imagePath': instance.imagePath,
      'action': _$TutorialActionEnumMap[instance.action],
      'actionData': instance.actionData,
      'isCompleted': instance.isCompleted,
    };

const _$TutorialActionEnumMap = {
  TutorialAction.addHarvest: 'addHarvest',
  TutorialAction.addBreeding: 'addBreeding',
  TutorialAction.addDirectPurchase: 'addDirectPurchase',
  TutorialAction.createProduct: 'createProduct',
  TutorialAction.craftProduct: 'craftProduct',
  TutorialAction.publishToMarket: 'publishToMarket',
  TutorialAction.sellProduct: 'sellProduct',
  TutorialAction.viewBalance: 'viewBalance',
  TutorialAction.viewReports: 'viewReports',
  TutorialAction.navigate: 'navigate',
};

TutorialProgress _$TutorialProgressFromJson(Map<String, dynamic> json) =>
    TutorialProgress(
      currentStepId: json['currentStepId'] as String,
      completedSteps: (json['completedSteps'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isCompleted: json['isCompleted'] as bool,
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$TutorialProgressToJson(TutorialProgress instance) =>
    <String, dynamic>{
      'currentStepId': instance.currentStepId,
      'completedSteps': instance.completedSteps,
      'isCompleted': instance.isCompleted,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };
