import 'package:json_annotation/json_annotation.dart';

part 'market_listing.g.dart';

@JsonSerializable()
class MarketListing {
  final String id;
  final String itemId; // ID del item en el inventario
  final String itemName;
  final int quantityListed; // Cantidad puesta en el mercado
  final int quantitySold; // Cantidad vendida
  final double pricePerUnit; // Precio de venta por unidad
  final double marketTaxPercentage; // Tasa de mercado (4% o 8%)
  final double publicationTaxPercentage; // Tasa de publicación (2.5%)
  final bool hasPremium; // Si tiene premium (4%) o no (8%)
  final DateTime listedAt; // Fecha de publicación
  final DateTime? lastUpdatedAt; // Última actualización
  final MarketListingStatus status;

  const MarketListing({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.quantityListed,
    required this.quantitySold,
    required this.pricePerUnit,
    required this.marketTaxPercentage,
    required this.publicationTaxPercentage,
    required this.hasPremium,
    required this.listedAt,
    this.lastUpdatedAt,
    required this.status,
  });

  factory MarketListing.fromJson(Map<String, dynamic> json) =>
      _$MarketListingFromJson(json);

  Map<String, dynamic> toJson() => _$MarketListingToJson(this);

  MarketListing copyWith({
    String? id,
    String? itemId,
    String? itemName,
    int? quantityListed,
    int? quantitySold,
    double? pricePerUnit,
    double? marketTaxPercentage,
    double? publicationTaxPercentage,
    bool? hasPremium,
    DateTime? listedAt,
    DateTime? lastUpdatedAt,
    MarketListingStatus? status,
  }) {
    return MarketListing(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      quantityListed: quantityListed ?? this.quantityListed,
      quantitySold: quantitySold ?? this.quantitySold,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      marketTaxPercentage: marketTaxPercentage ?? this.marketTaxPercentage,
      publicationTaxPercentage:
          publicationTaxPercentage ?? this.publicationTaxPercentage,
      hasPremium: hasPremium ?? this.hasPremium,
      listedAt: listedAt ?? this.listedAt,
      lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt,
      status: status ?? this.status,
    );
  }

  /// Cantidad restante en el mercado
  int get quantityRemaining => quantityListed - quantitySold;

  /// Ingresos brutos totales
  double get totalGrossRevenue => quantitySold * pricePerUnit;

  /// Tasa total de mercado (mercado + publicación)
  double get totalTaxPercentage =>
      marketTaxPercentage + publicationTaxPercentage;

  /// Impuestos totales pagados
  double get totalTaxesPaid => totalGrossRevenue * (totalTaxPercentage / 100);

  /// Ingresos netos (después de impuestos)
  double get totalNetRevenue => totalGrossRevenue - totalTaxesPaid;

  /// Costo de publicación inicial
  double get initialPublicationCost =>
      quantityListed * pricePerUnit * (publicationTaxPercentage / 100);

  /// Valor total en el mercado (cantidad restante × precio)
  double get marketValue => quantityRemaining * pricePerUnit;

  /// Verifica si la venta está completamente vendida
  bool get isFullySold => quantitySold >= quantityListed;

  /// Verifica si la venta está activa
  bool get isActive => status == MarketListingStatus.active && !isFullySold;
}

@JsonEnum()
enum MarketListingStatus {
  @JsonValue('active')
  active, // Activa en el mercado

  @JsonValue('sold')
  sold, // Completamente vendida

  @JsonValue('cancelled')
  cancelled, // Cancelada por el usuario

  @JsonValue('expired')
  expired, // Expirada
}

@JsonSerializable()
class SaleTransaction {
  final String id;
  final String marketListingId;
  final String itemName;
  final int quantitySold;
  final double pricePerUnit;
  final double grossRevenue;
  final double marketTax;
  final double publicationTax;
  final double netRevenue;
  final DateTime soldAt;

  const SaleTransaction({
    required this.id,
    required this.marketListingId,
    required this.itemName,
    required this.quantitySold,
    required this.pricePerUnit,
    required this.grossRevenue,
    required this.marketTax,
    required this.publicationTax,
    required this.netRevenue,
    required this.soldAt,
  });

  factory SaleTransaction.fromJson(Map<String, dynamic> json) =>
      _$SaleTransactionFromJson(json);

  Map<String, dynamic> toJson() => _$SaleTransactionToJson(this);

  /// Crea una transacción de venta desde una venta en el mercado
  factory SaleTransaction.fromMarketSale({
    required MarketListing listing,
    required int quantitySold,
  }) {
    final grossRevenue = quantitySold * listing.pricePerUnit;
    final marketTax = grossRevenue * (listing.marketTaxPercentage / 100);
    // El publicationTax ya se descontó al publicar, no se descuenta nuevamente al vender
    final publicationTax = 0.0;
    final netRevenue = grossRevenue - marketTax;

    return SaleTransaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      marketListingId: listing.id,
      itemName: listing.itemName,
      quantitySold: quantitySold,
      pricePerUnit: listing.pricePerUnit,
      grossRevenue: grossRevenue,
      marketTax: marketTax,
      publicationTax: publicationTax,
      netRevenue: netRevenue,
      soldAt: DateTime.now(),
    );
  }
}

/// Representa una pérdida por cancelación de publicación
@JsonSerializable()
class PublicationLoss {
  final String id;
  final String marketListingId;
  final String itemName;
  final int quantityCancelled;
  final double pricePerUnit;
  final double
  publicationTaxLost; // Tasa de publicación perdida (no reembolsable)
  final DateTime cancelledAt;
  final String reason; // Motivo de la cancelación

  const PublicationLoss({
    required this.id,
    required this.marketListingId,
    required this.itemName,
    required this.quantityCancelled,
    required this.pricePerUnit,
    required this.publicationTaxLost,
    required this.cancelledAt,
    required this.reason,
  });

  factory PublicationLoss.fromJson(Map<String, dynamic> json) =>
      _$PublicationLossFromJson(json);

  Map<String, dynamic> toJson() => _$PublicationLossToJson(this);

  /// Crea una pérdida por cancelación desde una publicación cancelada
  factory PublicationLoss.fromCancelledListing({
    required MarketListing listing,
    required String reason,
  }) {
    final quantityCancelled = listing.quantityRemaining;
    final publicationTaxLost =
        quantityCancelled *
        listing.pricePerUnit *
        (listing.publicationTaxPercentage / 100);

    return PublicationLoss(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      marketListingId: listing.id,
      itemName: listing.itemName,
      quantityCancelled: quantityCancelled,
      pricePerUnit: listing.pricePerUnit,
      publicationTaxLost: publicationTaxLost,
      cancelledAt: DateTime.now(),
      reason: reason,
    );
  }
}
