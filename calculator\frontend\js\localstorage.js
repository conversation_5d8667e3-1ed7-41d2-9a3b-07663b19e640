// e:\herd\honoris\calculator\frontend\js\localstorage.js
'use strict';

// --- Constantes para LocalStorage ---
// Estas constantes son específicas para el historial de la calculadora de crafting.
// Si tienes otras calculadoras (ej. refinado) que usan localStorage,
// deberías usar claves diferentes para ellas.
const CRAFTING_PRODUCTS_HISTORY_KEY = 'craftingProductsHistory';
const CRAFTING_HISTORY_LIMIT = 10;

/**
 * Obtiene el historial de productos de crafting de localStorage.
 * @returns {Array<object>} Un array de objetos de productos guardados, o un array vacío si no hay.
 */
function getCraftingProductHistory() {
    try {
        const historyJson = localStorage.getItem(CRAFTING_PRODUCTS_HISTORY_KEY);
        return historyJson ? JSON.parse(historyJson) : [];
    } catch (e) {
        console.error("Error al leer el historial de crafting de localStorage:", e);
        return [];
    }
}

/**
 * Guarda un array de productos de crafting en localStorage.
 * @param {Array<object>} history - El array de productos a guardar.
 */
function saveCraftingProductHistory(history) {
    try {
        // Limitar el tamaño del historial antes de guardar
        if (history.length > CRAFTING_HISTORY_LIMIT) {
            history = history.slice(0, CRAFTING_HISTORY_LIMIT); // Conserva los más recientes
        }
        localStorage.setItem(CRAFTING_PRODUCTS_HISTORY_KEY, JSON.stringify(history));
        console.log("Historial de productos de crafting guardado en localStorage:", history);
    } catch (e) {
        console.error("Error al escribir el historial de crafting en localStorage:", e);
        alert("Error al guardar el historial de productos de crafting.");
    }
}

/**
 * Exporta el historial de productos de crafting a un archivo JSON.
 */
function exportCraftingHistoryToFile() {
    console.log("Intentando exportar historial de crafting...");
    const history = getCraftingProductHistory();
    if (history.length === 0) {
        alert("No hay historial de crafting para exportar.");
        console.warn("Exportación de crafting fallida: El historial está vacío.");
        return;
    }

    const historyJson = JSON.stringify(history, null, 2); // null, 2 para formatear el JSON y hacerlo legible
    const blob = new Blob([historyJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `crafting_products_history_backup_${new Date().toISOString().slice(0,10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log("Historial de crafting exportado exitosamente.");
    alert("Historial de crafting exportado. Revisa tus descargas.");
}

/**
 * Maneja la selección de un archivo para importar el historial de crafting.
 * @param {Event} event - El objeto evento del input file.
 * @param {function} [callbackOnSuccess] - Función a llamar después de importar y guardar exitosamente.
 *                                         Esta función debería encargarse de actualizar la UI (ej. redibujar la lista del historial).
 */
function importCraftingHistoryFromFile(event, callbackOnSuccess) {
    console.log("Intentando importar historial de crafting...");
    const file = event.target.files[0];
    const importHistoryInputElement = event.target; // Guardar referencia al input para poder resetearlo

    if (!file) {
        console.warn("Importación de crafting fallida: No se seleccionó ningún archivo.");
        return;
    }

    if (file.type !== "application/json") {
        alert("Por favor, selecciona un archivo JSON válido (terminado en .json) para el historial de crafting.");
        console.warn("Importación de crafting fallida: Tipo de archivo inválido.");
        if (importHistoryInputElement) importHistoryInputElement.value = ''; // Resetear el input
        return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            const importedHistory = JSON.parse(e.target.result);
            // Validación básica: asegurarse de que es un array
            if (Array.isArray(importedHistory)) {
                saveCraftingProductHistory(importedHistory); // Guarda en localStorage usando la función de este mismo archivo
                alert("Historial de crafting importado correctamente.");
                console.log("Historial de crafting importado exitosamente:", importedHistory);
                if (typeof callbackOnSuccess === 'function') {
                    callbackOnSuccess(); // Llama al callback para que el script principal actualice la UI
                }
            } else {
                throw new Error("El archivo no contiene un historial de crafting válido (debe ser un array).");
            }
        } catch (error) {
            console.error("Error al importar el historial de crafting:", error);
            alert(`Error al importar el historial de crafting: ${error.message}`);
        } finally {
            // Resetear el input de archivo para permitir importar el mismo archivo de nuevo si es necesario
            if (importHistoryInputElement) importHistoryInputElement.value = '';
        }
    };
    reader.readAsText(file);
}

// Si necesitas gestionar el localStorage para OTRAS calculadoras (ej. refinado),
// podrías añadir funciones similares aquí pero con diferentes CRAFTING_PRODUCTS_HISTORY_KEY
// y nombres de función (ej. getRefiningHistory, saveRefiningHistory, etc.).
