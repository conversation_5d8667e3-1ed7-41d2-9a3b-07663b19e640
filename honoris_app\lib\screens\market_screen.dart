import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/market_listing.dart';
import '../models/inventory_item.dart';
import '../models/forecast.dart';
import '../services/market_service.dart';
import '../services/inventory_service.dart';
import '../services/number_format_service.dart';
import '../services/forecast_service.dart';

class MarketScreen extends StatefulWidget {
  const MarketScreen({super.key});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<MarketListing> _activeListings = [];
  List<MarketListing> _soldListings = [];
  List<InventoryItem> _inventoryItems = [];
  List<SaleForecast> _forecasts = [];
  bool _isLoading = true;
  SortType _currentSortType = SortType.alphabetical;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final activeListings = await MarketService.getActiveListings();
      final soldListings = await MarketService.getSoldListings();
      final inventoryItems = await InventoryService.getInventoryItems();
      final forecasts = await ForecastService.loadForecasts();

      setState(() {
        _activeListings = activeListings;
        _soldListings = soldListings;
        _inventoryItems = inventoryItems;
        _forecasts = forecasts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error cargando datos: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mercado'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.onPrimary,
          unselectedLabelColor: Theme.of(
            context,
          ).colorScheme.onPrimary.withOpacity(0.7),
          indicatorColor: Theme.of(context).colorScheme.onPrimary,
          tabs: const [
            Tab(text: 'Vender', icon: Icon(Icons.sell)),
            Tab(text: 'En Mercado', icon: Icon(Icons.store)),
            Tab(text: 'Vendidos', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSellTab(),
                _buildActiveListingsTab(),
                _buildSoldListingsTab(),
              ],
            ),
    );
  }

  Widget _buildSellTab() {
    if (_inventoryItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay items en el inventario para vender'),
          ],
        ),
      );
    }

    // Obtener items con pronósticos
    final itemsWithForecasts = _getItemsWithForecasts();
    final sortedItems = _sortItemsWithForecasts(itemsWithForecasts);

    return Column(
      children: [
        // Filtro de ordenamiento
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Ordenar por: ',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              Expanded(
                child: DropdownButton<SortType>(
                  value: _currentSortType,
                  isExpanded: true,
                  onChanged: (SortType? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _currentSortType = newValue;
                      });
                    }
                  },
                  items: SortType.values.map((SortType type) {
                    return DropdownMenuItem<SortType>(
                      value: type,
                      child: Text(type.displayName),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        // Lista de items
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: sortedItems.length,
            itemBuilder: (context, index) {
              final itemData = sortedItems[index];
              return _buildEnhancedItemCard(itemData);
            },
          ),
        ),
      ],
    );
  }

  /// Combina items del inventario con sus pronósticos
  List<Map<String, dynamic>> _getItemsWithForecasts() {
    return _inventoryItems.map((item) {
      final forecast = _forecasts.firstWhere(
        (f) => f.itemName == item.name,
        orElse: () => SaleForecast(
          itemName: item.name,
          quantity: item.quantity,
          cost: item.price,
          lastSalePrice: 0.0,
          forecastPrice: 0.0,
          profit: 0.0,
          profitPercentage: 0.0,
          focusUsed: item.focusUsed * item.quantity,
          focusEfficiency: 0.0,
          createdAt: DateTime.now(),
        ),
      );

      return {'item': item, 'forecast': forecast};
    }).toList();
  }

  /// Ordena los items según el tipo de ordenamiento seleccionado
  List<Map<String, dynamic>> _sortItemsWithForecasts(
    List<Map<String, dynamic>> items,
  ) {
    final sortedItems = List<Map<String, dynamic>>.from(items);

    switch (_currentSortType) {
      case SortType.alphabetical:
        sortedItems.sort((a, b) {
          final itemA = a['item'] as InventoryItem;
          final itemB = b['item'] as InventoryItem;
          return itemA.name.toLowerCase().compareTo(itemB.name.toLowerCase());
        });
        break;
      case SortType.profitPercentage:
        sortedItems.sort((a, b) {
          final forecastA = a['forecast'] as SaleForecast;
          final forecastB = b['forecast'] as SaleForecast;
          return forecastB.profitPercentage.compareTo(
            forecastA.profitPercentage,
          );
        });
        break;
      case SortType.focusEfficiency:
        sortedItems.sort((a, b) {
          final forecastA = a['forecast'] as SaleForecast;
          final forecastB = b['forecast'] as SaleForecast;
          return forecastB.focusEfficiency.compareTo(forecastA.focusEfficiency);
        });
        break;
    }

    return sortedItems;
  }

  /// Construye una tarjeta mejorada para mostrar item con pronóstico
  Widget _buildEnhancedItemCard(Map<String, dynamic> itemData) {
    final item = itemData['item'] as InventoryItem;
    final forecast = itemData['forecast'] as SaleForecast;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    item.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _showSellDialog(item),
                  child: const Text('Vender'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoColumn(
                    'Cantidad',
                    NumberFormatService.formatNumber(
                      item.quantity,
                      decimals: 2,
                    ),
                  ),
                ),
                Expanded(
                  child: _buildInfoColumn(
                    'Costo',
                    NumberFormatService.formatCurrency(item.price),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoColumn(
                    'Último Precio',
                    forecast.lastSalePrice > 0
                        ? NumberFormatService.formatCurrency(
                            forecast.lastSalePrice,
                          )
                        : 'Sin ventas',
                  ),
                ),
                Expanded(
                  child: _buildInfoColumn(
                    'Precio Pronosticado',
                    forecast.forecastPrice > 0
                        ? NumberFormatService.formatCurrency(
                            forecast.forecastPrice,
                          )
                        : 'Sin pronóstico',
                  ),
                ),
              ],
            ),
            if (forecast.profit != 0 || forecast.profitPercentage != 0) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoColumn(
                      'Ganancia',
                      NumberFormatService.formatProfit(forecast.profit),
                    ),
                  ),
                  Expanded(
                    child: _buildInfoColumn(
                      '% Ganancia',
                      NumberFormatService.formatPercentage(
                        forecast.profitPercentage / 100,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (item.focusUsed > 0 && forecast.focusEfficiency > 0) ...[
              const SizedBox(height: 8),
              _buildInfoColumn(
                'Eficiencia de Foco',
                '${NumberFormatService.formatFocusEfficiency(forecast.profit, forecast.focusUsed)} por foco',
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Construye una columna de información
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildActiveListingsTab() {
    if (_activeListings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay productos en el mercado'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activeListings.length,
      itemBuilder: (context, index) {
        final listing = _activeListings[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        listing.itemName,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: listing.hasPremium
                            ? Colors.green[100]
                            : Colors.orange[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        listing.hasPremium ? 'Premium 4%' : 'Normal 8%',
                        style: TextStyle(
                          fontSize: 12,
                          color: listing.hasPremium
                              ? Colors.green[800]
                              : Colors.orange[800],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Precio/Unidad',
                        NumberFormatService.formatCurrency(
                          listing.pricePerUnit,
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Cantidad',
                        '${listing.quantityRemaining}/${listing.quantityListed}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Valor en Mercado',
                        NumberFormatService.formatCurrency(listing.marketValue),
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Vendido',
                        NumberFormatService.formatCurrency(
                          listing.totalNetRevenue,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _showUpdateSalesDialog(listing),
                        child: const Text('Actualizar Ventas'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => _cancelListing(listing),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Cancelar'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSoldListingsTab() {
    if (_soldListings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay ventas completadas'),
          ],
        ),
      );
    }

    // Ordenar por fecha más reciente primero
    final sortedSoldListings = List<MarketListing>.from(_soldListings);
    sortedSoldListings.sort((a, b) {
      // Usar lastUpdatedAt si está disponible, sino listedAt
      final aDate = a.lastUpdatedAt ?? a.listedAt;
      final bDate = b.lastUpdatedAt ?? b.listedAt;
      return bDate.compareTo(aDate); // Orden descendente (más reciente primero)
    });

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedSoldListings.length,
      itemBuilder: (context, index) {
        final listing = sortedSoldListings[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  listing.itemName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Cantidad Vendida',
                        '${listing.quantitySold}',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Precio/Unidad',
                        NumberFormatService.formatCurrency(
                          listing.pricePerUnit,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Ingresos Brutos',
                        NumberFormatService.formatCurrency(
                          listing.totalGrossRevenue,
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Ingresos Netos',
                        NumberFormatService.formatCurrency(
                          listing.totalNetRevenue,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildInfoItem(
                  'Impuestos Pagados',
                  NumberFormatService.formatCurrency(listing.totalTaxesPaid),
                ),
                const SizedBox(height: 8),
                FutureBuilder<double>(
                  future: _calculateProfit(listing),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final profit = snapshot.data!;
                      final profitPercentage = _calculateProfitPercentage(
                        listing,
                        profit,
                      );
                      return _buildInfoItem(
                        'Ganancia',
                        '${NumberFormatService.formatProfit(profit)} (${NumberFormatService.formatPercentage(profitPercentage / 100)})',
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Calcula la ganancia de un listing
  Future<double> _calculateProfit(MarketListing listing) async {
    final inventoryItems = await InventoryService.getInventoryItems();
    final inventoryItem = inventoryItems.firstWhere(
      (item) => item.name == listing.itemName,
      orElse: () => InventoryItem(
        id: '',
        name: listing.itemName,
        quantity: 0,
        price: 0.0,
        focusUsed: 0.0,
        origin: ItemOrigin.directPurchase,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isRawMaterial: true,
      ),
    );

    final productCost = inventoryItem.price * listing.quantitySold;
    return listing.totalNetRevenue - productCost;
  }

  /// Calcula el porcentaje de ganancia
  double _calculateProfitPercentage(MarketListing listing, double profit) {
    final inventoryItem = _inventoryItems.firstWhere(
      (item) => item.name == listing.itemName,
      orElse: () => InventoryItem(
        id: '',
        name: listing.itemName,
        quantity: 0,
        price: 0.0,
        focusUsed: 0.0,
        origin: ItemOrigin.directPurchase,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isRawMaterial: true,
      ),
    );

    final productCost = inventoryItem.price * listing.quantitySold;
    if (productCost == 0) return 0.0;
    return (profit / productCost) * 100;
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _showSellDialog(InventoryItem item) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    bool hasPremium = true;
    bool isDirectSale = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Vender ${item.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: quantityController,
                decoration: InputDecoration(
                  labelText: 'Cantidad a vender',
                  hintText:
                      'Máximo disponible: ${NumberFormatService.formatNumber(item.quantity, decimals: 2)}',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.all_inclusive),
                    onPressed: () {
                      quantityController.text =
                          NumberFormatService.formatNumber(
                            item.quantity,
                            decimals: 2,
                          ).replaceAll('\$', '');
                    },
                    tooltip: 'Usar máximo disponible',
                  ),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: priceController,
                decoration: InputDecoration(
                  labelText: 'Precio por unidad',
                  hintText:
                      'Costo: ${NumberFormatService.formatCurrency(item.price)}',
                  border: const OutlineInputBorder(),
                  prefixText: '\$',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Venta Directa (0% tasas)'),
                subtitle: const Text('Sin tasas de mercado ni publicación'),
                value: isDirectSale,
                onChanged: (bool? value) {
                  setDialogState(() {
                    isDirectSale = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              if (!isDirectSale) ...[
                const SizedBox(height: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Tipo de Cuenta'),
                    const SizedBox(height: 8),
                    SegmentedButton<bool>(
                      segments: const [
                        ButtonSegment(value: true, label: Text('Premium (4%)')),
                        ButtonSegment(value: false, label: Text('Normal (8%)')),
                      ],
                      selected: {hasPremium},
                      onSelectionChanged: (Set<bool> selection) {
                        setDialogState(() {
                          hasPremium = selection.first;
                        });
                      },
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isDirectSale ? Colors.green[50] : Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isDirectSale
                        ? Colors.green[200]!
                        : Colors.blue[200]!,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isDirectSale
                          ? 'Venta Directa:'
                          : 'Costos de Publicación:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isDirectSale
                            ? Colors.green[800]
                            : Colors.blue[800],
                      ),
                    ),
                    if (isDirectSale) ...[
                      const Text('• Sin tasas de mercado: 0%'),
                      const Text('• Sin tasas de publicación: 0%'),
                      const Text('• Total: 0%'),
                    ] else ...[
                      Text('• Tasa de mercado: ${hasPremium ? "4%" : "8%"}'),
                      const Text('• Tasa de publicación: 2.5%'),
                      Text('• Total: ${hasPremium ? "6.5%" : "10.5%"}'),
                    ],
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () => _showForecast(
                item,
                quantityController,
                priceController,
                hasPremium,
                isDirectSale,
              ),
              child: const Text('Pronosticar'),
            ),
            ElevatedButton(
              onPressed: () => _sellItem(
                item,
                quantityController,
                priceController,
                hasPremium,
                isDirectSale,
              ),
              child: Text(isDirectSale ? 'Vender Directo' : 'Publicar'),
            ),
          ],
        ),
      ),
    );
  }

  /// Muestra el pronóstico de ganancia sin publicar
  void _showForecast(
    InventoryItem item,
    TextEditingController quantityController,
    TextEditingController priceController,
    bool hasPremium,
    bool isDirectSale,
  ) {
    final quantity = double.tryParse(quantityController.text) ?? 0;
    final price = double.tryParse(priceController.text) ?? 0;

    if (quantity <= 0 || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ingresa cantidad y precio válidos')),
      );
      return;
    }

    // Calcular pronóstico
    final grossRevenue = quantity * price;
    final marketTax = isDirectSale
        ? 0.0
        : grossRevenue * (hasPremium ? 0.04 : 0.08);
    final publicationTax = isDirectSale ? 0.0 : grossRevenue * 0.025;
    final netRevenue = grossRevenue - marketTax - publicationTax;
    final productCost = item.price * quantity;
    final profit = netRevenue - productCost;
    final profitPercentage = productCost > 0
        ? (profit / productCost) * 100
        : 0.0;
    final totalFocusUsed = item.focusUsed * quantity;
    final focusEfficiency = totalFocusUsed > 0 ? profit / totalFocusUsed : 0.0;

    // Guardar pronóstico
    final forecast = SaleForecast(
      itemName: item.name,
      quantity: quantity,
      cost: item.price,
      lastSalePrice: 0.0, // Se actualizará con datos reales
      forecastPrice: price,
      profit: profit,
      profitPercentage: profitPercentage,
      focusUsed: totalFocusUsed,
      focusEfficiency: focusEfficiency,
      createdAt: DateTime.now(),
    );

    ForecastService.saveForecast(forecast).then((_) {
      _loadData(); // Recargar datos para mostrar el pronóstico actualizado
    });

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Pronóstico - ${item.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildForecastItem(
              'Cantidad',
              NumberFormatService.formatNumber(quantity, decimals: 2),
            ),
            _buildForecastItem(
              'Precio por Unidad',
              NumberFormatService.formatCurrency(price),
            ),
            const Divider(),
            _buildForecastItem(
              'Ingresos Brutos',
              NumberFormatService.formatCurrency(grossRevenue),
            ),
            _buildForecastItem(
              'Impuesto de Mercado',
              NumberFormatService.formatCurrency(marketTax),
            ),
            _buildForecastItem(
              'Impuesto de Publicación',
              NumberFormatService.formatCurrency(publicationTax),
            ),
            _buildForecastItem(
              'Ingresos Netos',
              NumberFormatService.formatCurrency(netRevenue),
            ),
            const Divider(),
            _buildForecastItem(
              'Costo del Producto',
              NumberFormatService.formatCurrency(productCost),
            ),
            _buildForecastItem(
              'Ganancia',
              '${NumberFormatService.formatProfit(profit)} (${NumberFormatService.formatPercentage(profitPercentage / 100)})',
            ),
            if (item.focusUsed > 0)
              _buildForecastItem(
                'Eficiencia de Foco',
                '${NumberFormatService.formatFocusEfficiency(profit, item.focusUsed * quantity)} por foco',
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  Widget _buildForecastItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _showUpdateSalesDialog(MarketListing listing) {
    final soldController = TextEditingController(
      text: listing.quantitySold.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Actualizar Ventas: ${listing.itemName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Cantidad total publicada: ${listing.quantityListed}'),
            Text('Cantidad vendida actual: ${listing.quantitySold}'),
            const SizedBox(height: 16),
            TextFormField(
              controller: soldController,
              decoration: const InputDecoration(
                labelText: 'Nueva cantidad vendida',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Text(
                'Nota: Si aumentas la cantidad vendida, se cobrará una nueva tasa de publicación del 2.5% sobre las unidades adicionales vendidas.',
                style: TextStyle(fontSize: 12, color: Colors.orange[800]),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => _updateSales(listing, soldController),
            child: const Text('Actualizar'),
          ),
        ],
      ),
    );
  }

  void _cancelListing(MarketListing listing) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancelar Publicación'),
        content: Text(
          '¿Estás seguro de que quieres cancelar la venta de ${listing.itemName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Sí, Cancelar'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await MarketService.cancelMarketListing(listing.id);
      if (success) {
        _loadData();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Publicación cancelada')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error al cancelar la publicación')),
        );
      }
    }
  }

  void _sellItem(
    InventoryItem item,
    TextEditingController quantityController,
    TextEditingController priceController,
    bool hasPremium,
    bool isDirectSale,
  ) async {
    final quantity = double.tryParse(quantityController.text);
    final price = double.tryParse(priceController.text);

    if (quantity == null || quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ingrese una cantidad válida')),
      );
      return;
    }

    if (price == null || price <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Ingrese un precio válido')));
      return;
    }

    if (quantity > item.quantity) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No hay suficiente cantidad en inventario'),
        ),
      );
      return;
    }

    Navigator.of(context).pop();

    if (isDirectSale) {
      // Venta directa - registrar como vendido inmediatamente con 0% tasas
      final grossRevenue = quantity * price;
      final saleTransaction = SaleTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        marketListingId: 'direct_sale_${DateTime.now().millisecondsSinceEpoch}',
        itemName: item.name,
        quantitySold: quantity.toInt(),
        pricePerUnit: price,
        grossRevenue: grossRevenue,
        marketTax: 0.0, // Sin tasas para venta directa
        publicationTax: 0.0, // Sin tasas para venta directa
        netRevenue: grossRevenue, // Ingreso completo sin deducciones
        soldAt: DateTime.now(),
      );

      final success = await MarketService.saveSaleTransaction(saleTransaction);

      if (success) {
        // Reducir cantidad del inventario
        await InventoryService.consumeInventoryItem(item.id, quantity);
        _loadData();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Venta directa registrada exitosamente'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error al registrar la venta directa')),
        );
      }
    } else {
      // Venta normal en el mercado
      final success = await MarketService.listItemInMarket(
        item: item,
        quantity: quantity.toInt(),
        pricePerUnit: price,
        hasPremium: hasPremium,
      );

      if (success) {
        // Reducir cantidad del inventario
        await InventoryService.consumeInventoryItem(item.id, quantity);
        _loadData();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Producto publicado en el mercado')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error al publicar en el mercado')),
        );
      }
    }
  }

  void _updateSales(
    MarketListing listing,
    TextEditingController soldController,
  ) async {
    final newQuantitySold = int.tryParse(soldController.text);

    if (newQuantitySold == null || newQuantitySold < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ingrese una cantidad válida')),
      );
      return;
    }

    if (newQuantitySold > listing.quantityListed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No se puede vender más de lo publicado')),
      );
      return;
    }

    if (newQuantitySold < listing.quantitySold) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No se puede reducir la cantidad vendida'),
        ),
      );
      return;
    }

    Navigator.of(context).pop();

    final success = await MarketService.updateMarketListing(
      listingId: listing.id,
      newQuantitySold: newQuantitySold,
    );

    if (success) {
      _loadData();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Ventas actualizadas')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error al actualizar las ventas')),
      );
    }
  }
}
