import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/market_listing.dart';
import '../models/inventory_item.dart';
import '../services/market_service.dart';
import '../services/inventory_service.dart';

class MarketScreen extends StatefulWidget {
  const MarketScreen({super.key});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<MarketListing> _activeListings = [];
  List<MarketListing> _soldListings = [];
  List<InventoryItem> _inventoryItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final activeListings = await MarketService.getActiveListings();
      final soldListings = await MarketService.getSoldListings();
      final inventoryItems = await InventoryService.getInventoryItems();

      setState(() {
        _activeListings = activeListings;
        _soldListings = soldListings;
        _inventoryItems = inventoryItems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error cargando datos: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mercado'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.onPrimary,
          unselectedLabelColor: Theme.of(
            context,
          ).colorScheme.onPrimary.withOpacity(0.7),
          indicatorColor: Theme.of(context).colorScheme.onPrimary,
          tabs: const [
            Tab(text: 'Vender', icon: Icon(Icons.sell)),
            Tab(text: 'En Mercado', icon: Icon(Icons.store)),
            Tab(text: 'Vendidos', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSellTab(),
                _buildActiveListingsTab(),
                _buildSoldListingsTab(),
              ],
            ),
    );
  }

  Widget _buildSellTab() {
    if (_inventoryItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay items en el inventario para vender'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _inventoryItems.length,
      itemBuilder: (context, index) {
        final item = _inventoryItems[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(item.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Cantidad: ${item.quantity.toStringAsFixed(1)}'),
                Text('Costo: \$${item.price.toStringAsFixed(4)}'),
                if (item.focusUsed > 0)
                  Text('Foco: ${item.focusUsed.toStringAsFixed(4)}'),
              ],
            ),
            trailing: ElevatedButton(
              onPressed: () => _showSellDialog(item),
              child: const Text('Vender'),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActiveListingsTab() {
    if (_activeListings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay productos en el mercado'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activeListings.length,
      itemBuilder: (context, index) {
        final listing = _activeListings[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        listing.itemName,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: listing.hasPremium
                            ? Colors.green[100]
                            : Colors.orange[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        listing.hasPremium ? 'Premium 4%' : 'Normal 8%',
                        style: TextStyle(
                          fontSize: 12,
                          color: listing.hasPremium
                              ? Colors.green[800]
                              : Colors.orange[800],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Precio/Unidad',
                        '\$${listing.pricePerUnit.toStringAsFixed(4)}',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Cantidad',
                        '${listing.quantityRemaining}/${listing.quantityListed}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Valor en Mercado',
                        '\$${listing.marketValue.toStringAsFixed(2)}',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Vendido',
                        '\$${listing.totalNetRevenue.toStringAsFixed(2)}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _showUpdateSalesDialog(listing),
                        child: const Text('Actualizar Ventas'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => _cancelListing(listing),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Cancelar'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSoldListingsTab() {
    if (_soldListings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay ventas completadas'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _soldListings.length,
      itemBuilder: (context, index) {
        final listing = _soldListings[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  listing.itemName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Cantidad Vendida',
                        '${listing.quantitySold}',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Precio/Unidad',
                        '\$${listing.pricePerUnit.toStringAsFixed(4)}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Ingresos Brutos',
                        '\$${listing.totalGrossRevenue.toStringAsFixed(2)}',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'Ingresos Netos',
                        '\$${listing.totalNetRevenue.toStringAsFixed(2)}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildInfoItem(
                  'Impuestos Pagados',
                  '\$${listing.totalTaxesPaid.toStringAsFixed(2)}',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _showSellDialog(InventoryItem item) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    bool hasPremium = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Vender ${item.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: quantityController,
                decoration: InputDecoration(
                  labelText: 'Cantidad a vender',
                  hintText: 'Máximo: ${item.quantity.toStringAsFixed(1)}',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: priceController,
                decoration: const InputDecoration(
                  labelText: 'Precio por unidad',
                  border: OutlineInputBorder(),
                  prefixText: '\$',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Tipo de cuenta:'),
                  const SizedBox(width: 16),
                  Expanded(
                    child: SegmentedButton<bool>(
                      segments: const [
                        ButtonSegment(value: true, label: Text('Premium (4%)')),
                        ButtonSegment(value: false, label: Text('Normal (8%)')),
                      ],
                      selected: {hasPremium},
                      onSelectionChanged: (Set<bool> selection) {
                        setDialogState(() {
                          hasPremium = selection.first;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Costos de Publicación:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[800],
                      ),
                    ),
                    Text('• Tasa de mercado: ${hasPremium ? "4%" : "8%"}'),
                    Text('• Tasa de publicación: 2.5%'),
                    Text('• Total: ${hasPremium ? "6.5%" : "10.5%"}'),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => _sellItem(
                item,
                quantityController,
                priceController,
                hasPremium,
              ),
              child: const Text('Publicar'),
            ),
          ],
        ),
      ),
    );
  }

  void _showUpdateSalesDialog(MarketListing listing) {
    final soldController = TextEditingController(
      text: listing.quantitySold.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Actualizar Ventas: ${listing.itemName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Cantidad total publicada: ${listing.quantityListed}'),
            Text('Cantidad vendida actual: ${listing.quantitySold}'),
            const SizedBox(height: 16),
            TextFormField(
              controller: soldController,
              decoration: const InputDecoration(
                labelText: 'Nueva cantidad vendida',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Text(
                'Nota: Si aumentas la cantidad vendida, se cobrará una nueva tasa de publicación del 2.5% sobre las unidades adicionales vendidas.',
                style: TextStyle(fontSize: 12, color: Colors.orange[800]),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => _updateSales(listing, soldController),
            child: const Text('Actualizar'),
          ),
        ],
      ),
    );
  }

  void _cancelListing(MarketListing listing) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancelar Publicación'),
        content: Text(
          '¿Estás seguro de que quieres cancelar la venta de ${listing.itemName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Sí, Cancelar'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await MarketService.cancelMarketListing(listing.id);
      if (success) {
        _loadData();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Publicación cancelada')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error al cancelar la publicación')),
        );
      }
    }
  }

  void _sellItem(
    InventoryItem item,
    TextEditingController quantityController,
    TextEditingController priceController,
    bool hasPremium,
  ) async {
    final quantity = double.tryParse(quantityController.text);
    final price = double.tryParse(priceController.text);

    if (quantity == null || quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ingrese una cantidad válida')),
      );
      return;
    }

    if (price == null || price <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Ingrese un precio válido')));
      return;
    }

    if (quantity > item.quantity) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No hay suficiente cantidad en inventario'),
        ),
      );
      return;
    }

    Navigator.of(context).pop();

    final success = await MarketService.listItemInMarket(
      item: item,
      quantity: quantity.toInt(),
      pricePerUnit: price,
      hasPremium: hasPremium,
    );

    if (success) {
      // Reducir cantidad del inventario
      await InventoryService.consumeInventoryItem(item.name, quantity);
      _loadData();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Producto publicado en el mercado')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error al publicar en el mercado')),
      );
    }
  }

  void _updateSales(
    MarketListing listing,
    TextEditingController soldController,
  ) async {
    final newQuantitySold = int.tryParse(soldController.text);

    if (newQuantitySold == null || newQuantitySold < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ingrese una cantidad válida')),
      );
      return;
    }

    if (newQuantitySold > listing.quantityListed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No se puede vender más de lo publicado')),
      );
      return;
    }

    if (newQuantitySold < listing.quantitySold) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No se puede reducir la cantidad vendida'),
        ),
      );
      return;
    }

    Navigator.of(context).pop();

    final success = await MarketService.updateMarketListing(
      listingId: listing.id,
      newQuantitySold: newQuantitySold,
    );

    if (success) {
      _loadData();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Ventas actualizadas')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error al actualizar las ventas')),
      );
    }
  }
}
