import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/crafting_provider.dart';
import '../models/harvest_data.dart';
import '../services/number_format_service.dart';
import '../services/tutorial_service.dart';

class AddHarvestScreen extends StatefulWidget {
  const AddHarvestScreen({super.key});

  @override
  State<AddHarvestScreen> createState() => _AddHarvestScreenState();
}

class _AddHarvestScreenState extends State<AddHarvestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cropNameController = TextEditingController();
  final _seedPriceController = TextEditingController();
  final _seedsPurchasedController = TextEditingController();
  final _seedsReceivedController = TextEditingController();
  final _harvestReceivedController = TextEditingController();
  final _focusPerSeedController = TextEditingController();

  bool _purchasedInMarket = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _cropNameController.dispose();
    _seedPriceController.dispose();
    _seedsPurchasedController.dispose();
    _seedsReceivedController.dispose();
    _harvestReceivedController.dispose();
    _focusPerSeedController.dispose();
    super.dispose();
  }

  void _autoFillTutorialData() {
    TutorialService.autoFillHarvestForm(
      itemController: _cropNameController,
      quantityController: _harvestReceivedController,
      seedsUsedController: _seedsPurchasedController,
      seedsReturnedController: _seedsReceivedController,
      seedPriceController: _seedPriceController,
      focusController: _focusPerSeedController,
    );

    // Show detailed tutorial message
    TutorialService.showTutorialMessage(
      context,
      'Paso 1: Cosecha de Maíz - Datos Explicados',
      '📊 EXPLICACIÓN DE LOS DATOS:\n\n'
          '🌱 Semillas Compradas: 18 semillas\n'
          '💰 Precio por semilla: 26.010 monedas\n'
          '📦 Semillas Recibidas: 15 (las que sobran)\n'
          '🌽 Maíz Cosechado: 152 unidades\n\n'
          '💡 ¿Por qué estos números?\n'
          'Compramos 18 semillas, plantamos todas, pero solo recuperamos 15 semillas. '
          'Las 3 semillas "perdidas" se convirtieron en 152 unidades de maíz.\n\n'
          '⚠️ IMPORTANTE: Ahora debes hacer clic en "Guardar en Inventario" para continuar.',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Agregar Cosecha'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Información de la Cosecha',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        ElevatedButton.icon(
                          onPressed: _autoFillTutorialData,
                          icon: const Icon(Icons.auto_fix_high, size: 16),
                          label: const Text('Tutorial'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _cropNameController,
                      decoration: const InputDecoration(
                        labelText: 'Nombre del Cultivo',
                        hintText: 'Ej: Milerama, Gordolobo, etc.',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'El nombre del cultivo es requerido';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _seedPriceController,
                      decoration: const InputDecoration(
                        labelText: 'Precio de la Semilla',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '\$ ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'El precio de la semilla es requerido';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price <= 0) {
                          return 'Ingresa un precio válido mayor a 0';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    CheckboxListTile(
                      title: const Text('Comprado en el Mercado'),
                      subtitle: const Text('Se agregará 2.5% de tasa'),
                      value: _purchasedInMarket,
                      onChanged: (value) {
                        setState(() {
                          _purchasedInMarket = value ?? false;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Datos de Siembra y Cosecha',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _seedsPurchasedController,
                            decoration: const InputDecoration(
                              labelText: 'Semillas Compradas',
                              hintText: '0',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final seeds = int.tryParse(value);
                              if (seeds == null || seeds <= 0) {
                                return 'Debe ser mayor a 0';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _seedsReceivedController,
                            decoration: const InputDecoration(
                              labelText: 'Semillas Recibidas',
                              hintText: '0',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Requerido';
                              }
                              final received = int.tryParse(value);
                              final purchased = int.tryParse(
                                _seedsPurchasedController.text,
                              );
                              if (received == null || received < 0) {
                                return 'No puede ser negativo';
                              }
                              if (purchased != null && received > purchased) {
                                return 'No puede ser mayor a las compradas';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _harvestReceivedController,
                      decoration: const InputDecoration(
                        labelText: 'Cosecha Recibida',
                        hintText: '0',
                        border: OutlineInputBorder(),
                        helperText: 'Cantidad total de producto cosechado',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'La cosecha recibida es requerida';
                        }
                        final harvest = int.tryParse(value);
                        if (harvest == null || harvest <= 0) {
                          return 'Debe ser mayor a 0';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _focusPerSeedController,
                      decoration: const InputDecoration(
                        labelText: 'Foco por Semilla (Opcional)',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        helperText: 'Foco usado por cada semilla plantada',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final focus = double.tryParse(value);
                          if (focus == null || focus < 0) {
                            return 'El foco no puede ser negativo';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Mostrar cálculos en tiempo real
            _buildCalculationPreview(),

            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveHarvest,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Guardar en Inventario'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationPreview() {
    // Intentar calcular con los valores actuales
    try {
      final harvestData = _createHarvestData();
      final errors = harvestData.validate();

      if (errors.isNotEmpty) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Vista Previa del Cálculo',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Completa todos los campos para ver el cálculo',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        );
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vista Previa del Cálculo',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildPreviewItem(
                      'Precio por Unidad',
                      NumberFormatService.formatCurrency(
                        harvestData.pricePerUnit,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _buildPreviewItem(
                      'Foco por Unidad',
                      NumberFormatService.formatNumber(
                        harvestData.focusPerUnit,
                        decimals: 2,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              _buildPreviewItem(
                'Valor Total del Inventario',
                NumberFormatService.formatCurrency(
                  harvestData.harvestReceived * harvestData.pricePerUnit,
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  Widget _buildPreviewItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  HarvestData _createHarvestData() {
    return HarvestData(
      cropName: _cropNameController.text.trim(),
      seedPrice: double.tryParse(_seedPriceController.text) ?? 0,
      purchasedInMarket: _purchasedInMarket,
      seedsPurchased: int.tryParse(_seedsPurchasedController.text) ?? 0,
      seedsReceived: int.tryParse(_seedsReceivedController.text) ?? 0,
      harvestReceived: int.tryParse(_harvestReceivedController.text) ?? 0,
      focusPerSeed: double.tryParse(_focusPerSeedController.text) ?? 0,
    );
  }

  Future<void> _saveHarvest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final harvestData = _createHarvestData();
      final errors = harvestData.validate();

      if (errors.isNotEmpty) {
        _showErrorDialog(errors.join('\n'));
        return;
      }

      final provider = context.read<CraftingProvider>();
      final success = await provider.addFromHarvest(harvestData);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cosecha agregada al inventario exitosamente'),
              backgroundColor: Colors.green,
            ),
          );

          // Continuar el tutorial si está activo
          TutorialService.continueAfterSave(context, 'harvest');

          Navigator.of(context).pop();
        } else {
          _showErrorDialog('Error al guardar la cosecha en el inventario');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error inesperado: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
