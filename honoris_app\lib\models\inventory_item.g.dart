// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InventoryItem _$InventoryItemFromJson(Map<String, dynamic> json) =>
    InventoryItem(
      id: json['id'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toDouble(),
      focusUsed: (json['focusUsed'] as num).toDouble(),
      origin: $enumDecode(_$ItemOriginEnumMap, json['origin']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isRawMaterial: json['isRawMaterial'] as bool,
    );

Map<String, dynamic> _$InventoryItemToJson(InventoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'quantity': instance.quantity,
      'focusUsed': instance.focusUsed,
      'origin': _$ItemOriginEnumMap[instance.origin]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isRawMaterial': instance.isRawMaterial,
    };

const _$ItemOriginEnumMap = {
  ItemOrigin.harvest: 'harvest',
  ItemOrigin.breeding: 'breeding',
  ItemOrigin.directPurchase: 'directPurchase',
  ItemOrigin.crafted: 'crafted',
};
